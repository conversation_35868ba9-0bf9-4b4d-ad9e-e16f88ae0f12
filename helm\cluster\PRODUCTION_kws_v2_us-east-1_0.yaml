defaultEnvironment: &defaultEnvironment
  AUDIT_KAFKA_TOPIC: freekws.us-east-1.audit
  CURRENT_REGION: us-east-1
  DD_ENV: production
  DD_PROFILING_ENABLED: false
  DD_RUNTIME_METRICS_ENABLED: false
  DD_TRACE_AGENT_URL: http://**********:8126
  DD_TRACE_ENABLED: false
  DEV_PORTAL_URL: https://portal-api-internal.kidswebservices.com
  DATABASE_READ_URL: vault:superawesome/kws/production/freekws-classic-wrapper-backend/DATABASE_READ_URL_US_EAST_1?version=1
  DATABASE_URL: vault:superawesome/kws/production/freekws-classic-wrapper-backend/DATABASE_URL_US_EAST_1?version=1
  ENVIRONMENT: production
  NODE_ENV: production
  KAFKA_HOST: b-2.kafkakwsv2productionf.lidywb.c21.kafka.us-east-1.amazonaws.com:9092,b-3.kafkakwsv2productionf.lidywb.c21.kafka.us-east-1.amazonaws.com:9092,b-1.kafkakwsv2productionf.lidywb.c21.kafka.us-east-1.amazonaws.com:9092
  KAFKA_OUT_OF_ORDER_TOPIC: freekws.us-east-1.out-of-order
  KEYCLOAK_ADDITIONAL_TRUSTED_ISSUERS: "https://auth.kidswebservices.com/auth/realms/kws,https://auth2.kidswebservices.com/auth/realms/kws"
  KEYCLOAK_BASE_URL: https://keycloak-internal.kidswebservices.com/auth
  KEYCLOAK_CLIENT_SECRET: vault:superawesome/kws/production/freekws-classic-wrapper-backend/KEYCLOAK_CLIENT_SECRET?version=1
  KEYCLOAK_REALM_URL: https://keycloak-internal.kidswebservices.com/auth/realms/kws
  KEYCLOAK_TIMEOUT_MS: 30000
  AGE_GATE_SERVICE_URL: https://agegate-api-us-eu-internal.kidswebservices.com
  SETTINGS_BACKEND_SERVICE_URL: https://settings-api-us-east-1-internal.kidswebservices.com
  FAMILY_SERVICE_SERVICE_URL: https://family-service-api-us-east-1-internal.kidswebservices.com
  PREVERIFICATION_SERVICE_SERVICE_URL: https://preverification-service-internal.kidswebservices.com
  ENCRYPTION_SECRET_KEYS: vault:superawesome/kws/production/freekws-shared/ENCRYPTION_SECRET_KEYS?version=4
  ENCRYPTION_SECRET_VERSION: vault:superawesome/kws/production/freekws-shared/ENCRYPTION_SECRET_VERSION?version=2
  ANALYTICS_SERVICE_URL: https://events2.v1production.kidswebservices.com
  ANALYTICS_SERVICE_AUTH_STRING: vault:superawesome/kws/production/freekws-classic-wrapper-backend/ANALYTICS_SERVICE_AUTH_STRING?version=1
  APP_WEBHOOKS_KAFKA_TOPIC: freekws.us-east-1.webhook
  CALLBACK_API_URL: https://callback-service-internal.kidswebservices.com
  BADGER_API_KEY: vault:superawesome/kws/production/freekws-classic-wrapper-backend/BADGER_API_KEY
  TALON_FLOW_ID: vault:superawesome/kws/production/globals/TALON_FLOW_ID
  TALON_API_KEY: vault:superawesome/kws/production/freekws-classic-wrapper-backend/TALON_API_KEY
  APP_WEBHOOKS_SEND_WEBHOOKS: vault:superawesome/kws/production/freekws-classic-wrapper-backend/APP_WEBHOOKS_SEND_WEBHOOKS
  EMAIL_KAFKA_TOPIC: freekws.us-east-1.email
  EMAIL_SA_LOGO_SRC: https://branding.kidswebservices.com/defaultBranding/footer-logo-default-light-hq.png
  BRANDING_API_URL: https://branding-service-internal.kidswebservices.com
  AMPLITUDE_API_KEY: vault:superawesome/kws/production/freekws-shared/AMPLITUDE_API_KEY?version=1
  AMPLITUDE_SERVER_URL: https://analytics.kidswebservices.com/v1/analytics/amplitude/http-api
  NIANTIC_FRONTEND_AUTH_URL: vault:superawesome/kws/production/customer-globals/niantic/SSO_URL
  NIANTIC_APP_DISPLAY_NAME: "Niantic Kids"
  NIANTIC_APP_CLIENT_ID: "superawesomeclub"
  NIANTIC_RESET_PASSWORD_EXPIRY_IN_HOURS: 1
  NIANTIC_SUPPORT_EMAIL_ADDRESS: "<EMAIL>"
freekws-classic-wrapper-backend-sa-deployment:
  autoscaling:
    replicas:
      fallback: 2
      max: 200
      min: 20
  containers:
    freekws-classic-wrapper-backend-api:
      env:
        <<: *defaultEnvironment
        DD_SERVICE: freekws-classic-wrapper-backend
      ports:
        - class: alb
          containerPort: 80
          customWebACL: 1
          groupName: freekws-classic-wrapper-backend-internal
          healthcheck: /healthcheck
          hosts:
            - classic-wrapper-api-internal.kidswebservices.com
            - classic-wrapper-api-us-east-1-internal.kidswebservices.com
          name: internal
          path: /*
          scheme: internal
          servicePort: 80
          type: Ingress
          annotations:
            alb.ingress.kubernetes.io/subnets: subnet-09be9c4901399bd1c,subnet-025f7ee63e7891047,subnet-031774d52cd779c9b
            alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
        - class: alb
          containerPort: 80
          customWebACL: 1
          groupName: freekws-classic-wrapper-backend
          healthcheck: /healthcheck
          hosts:
            - classic-wrapper-api.kidswebservices.com
            - classic-wrapper-api-us-east-1.kidswebservices.com
            - classic-wrapper-api-us.kidswebservices.com
          name: http
          path: /*
          scheme: internet-facing
          servicePort: 80
          type: Ingress
          annotations:
            alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
sa-overprovisioner:
  containers:
    - name: freekws-classic-wrapper-backend
      image:
        name: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend
      requests:
        cpu: 1000m

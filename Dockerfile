FROM artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-nodejs:22.x-builder AS builder-base

ADD package.json .
ADD package-lock.json .
ADD packages/common/package.json packages/common/package.json

FROM builder-base AS builder
ARG ARTIFACTORY_TOKEN_PLATFORM

RUN npm ci && npm cache clean --force

ADD packages/ packages/
ADD tsconfig.json .
ADD tsconfig.packages.json .
ADD .eslintrc.js .

RUN npm run packages:build

ADD config/ config/
ADD src/ src/
ADD scripts/ scripts/
ADD migration/ migration/

ADD tsconfig.build.json .
ADD ormconfig.ts .
ADD debug.js .

RUN find /srv/scripts -type f -name "*.sh" -exec dos2unix {} \;
RUN npm run build

ADD test-acceptance/ test-acceptance/
ADD test/ test/

FROM builder-base AS builder-prod
ARG ARTIFACTORY_TOKEN_PLATFORM

RUN npm ci --only=prod --no-optional && npm cache clean --force

FROM artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-nodejs:22.x AS service

COPY --from=builder-prod /srv/node_modules /srv/node_modules
COPY --from=builder /srv/dist /srv/dist
COPY --from=builder /srv/config /srv/config
COPY --from=builder /srv/packages /srv/packages

COPY --from=builder /srv/package.json /srv/package.json
COPY --from=builder /srv/package-lock.json /srv/package-lock.json

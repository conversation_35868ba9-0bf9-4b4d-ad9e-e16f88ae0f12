# k6 Load Testing

## Overview

This directory contains the load and performance tests for the classic wrapper
using [k6](https://k6.io/). Write-up of results can
be [found here](https://superawesomeltd.atlassian.net/wiki/spaces/KWS/pages/6612353025/Performance+Testing+Results).

The tests are written in TypeScript to leverage static typing and improve maintainability. However,
the target execution environment (Hatchery) only supports plain JavaScript. To accommodate this, we
use `esbuild` to transpile and bundle the TypeScript source code into a single JavaScript file for
deployment. Copy and paste the build contents into the Hatchery UI to execute the tests.

There are two primary test suites:

1. **Sequential Scenarios:** Tests a series of core API endpoints in a predefined sequence.
2. **Webhooks:** Tests the webhook handlers for various event types.

## General Setup

### Prerequisites

Before you can run the tests, you must have the following tools installed:

* [Node.js](https://nodejs.org/) (which includes npm)
* [k6](https://k6.io/docs/getting-started/installation/)

Install dependencies with:

```bash
npm install
```

### Deployment to Hatchery

To deploy these tests to Hatchery, you must first build the JavaScript output files.

1. **Run the build script** for the test suite you want to deploy:
    * `npm run build:sequential-scenarios`
    * `npm run build:webhook`
2. **Locate the output:** The bundled JavaScript file will be created in the `dist/` directory (
   e.g., `dist/sequential-scenarios-test.js`).
3. **Copy the content** of the generated `.js` file and paste it into the Hatchery UI.

---

## 1. Sequential Scenarios Test

This suite tests a series of core API endpoints in a predefined sequence, as defined in
`sequential-scenarios-test.ts`. Where possible, data set-up is done within the test itself.

To run these tests at high loads, you may find you need to disable rate limits within the settings
service and consent backend for `send-consent-email` related endpoints specifically.

### Running the Test Locally

```bash
npm run test:sequential-scenarios
```

### Configuration

Create a `.env` file in the `k6/` directory with the following variables. See `example.env` for a
template.

* `BASE_URL`: **(Required)** The base URL of the application under test (e.g.,
  `http://localhost:7001`).
* `CLIENT_ID`: **(Required)** The OAuth client ID for a test application.
* `CLIENT_SECRET`: **(Required)** The OAuth client secret for a test application.
* `DOM_CRAFT_CLIENT_ID`: **(Required)** The specific client ID for the "Dom Craft" test application.
* `DOM_CRAFT_SECRET`: **(Required)** The specific client secret for the "Dom Craft" test
  application.
* `TARGET_QPS`: (Optional) The target Queries Per Second for the test. Defaults to `100`.
* `TEST_DURATION`: (Optional) The duration in seconds to test each endpoint. Defaults to `60`.
* `COOLDOWN_DURATION`: (Optional) The cooldown period in seconds between testing different
  endpoints. Defaults to `60`.

### How to Add a New Sequential Test

1. **Define the Request:** Open `k6/requests.ts` and add a new function to the `requests` object.
   This function should contain the `http` call and `check` assertions for the new endpoint.
2. **Add to Sequence:** Open `k6/sequential-scenarios-test.ts`.
    * Import your new request function if needed.
    * Add a new entry to the `endpointSequence` array, specifying a `name` and the `func` you just
      created.
3. **Export the Executor:** Still in `k6/sequential-scenarios-test.ts`, create and export a new
   function that k6 will use to execute your test. This function should match the `name` from the
   sequence and call your request function.

---

## 2. Webhook Load Test

This suite tests the webhook handlers for various event types, as defined in
`webhook-load-tests.ts`.

### Running the Test Locally

```bash
npm run test:webhooks
```

### Configuration

Create a `.env` file in the `k6/` directory with the following variables. See `example.env` for a
template.

* `BASE_URL`: **(Required)** The base URL of the application under test (e.g.,
  `http://localhost:7001`).
* `WEBHOOK_TYPE`: (Optional) Specifies a single webhook to test. If not set, it defaults to `all`,
  which tests all webhooks in a round-robin fashion. Valid options are:
    * `child-graduated`
    * `settings-changed`
    * `user-removed`
    * `user-added`
    * `guardian-expired`
    * `family-deleted`

> **Note on Webhook Secrets:**
> Currently, the secrets used to sign webhook payloads (`x-kws-signature`) are hardcoded in
`k6/web-endpoints.ts`. Webhooks have changed since these tests were run, so this may now be
> incorrect and require updating.

### How to Add a New Webhook Test

1. **Add Payload:** Open `k6/webhook-payloads.ts`. Define and export a new payload object for your
   webhook.
2. **Add Endpoint:** Open `k6/web-endpoints.ts`. Create and export a new function that sends the
   request for the new webhook, including signature generation.
3. **Add to Executor:** Open `k6/webhook-load-tests.ts`.
    * Add the new webhook type to the `allWebhookTypes` array.
    * Add a new `case` to the `switch` statement to handle the new `WEBHOOK_TYPE` and call the
      endpoint function you created.

# Out of Memory Gotcha

If you find your K6 test is terminated without reason on hatchery, it's likely due to it running out
of memory.

The common culprit for this is the metrics K6 sends to Prometheus. Any URL with unique path params
will cause an explosion of unique metrics for K6 to track, if the test runs long enough, it'll run
out of memory this way.

To circumvent this system tags has been explicitly set:

```ts
 systemTags: ['method', 'status', 'scenario']
```

This excludes the default URL tag. You can still set this tag manually in your requests, if needs
be.

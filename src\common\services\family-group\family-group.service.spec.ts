import { TestingModule } from '@nestjs/testing';
import { createClientMock } from '@superawesome/freekws-clients-base/src/test-utils/mock';
import { FAMILY_SERVICE_CLIENT_INJECT_KEY, familyServicePlugin } from '@superawesome/freekws-family-service-common';

import { FamilyGroupService } from './family-group.service';
import { Testing } from '../../utils';

const mockFamilyApi = createClientMock(familyServicePlugin, jest.fn);

describe('FamilyGroupService', () => {
  let service: FamilyGroupService;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [FamilyGroupService, { provide: FAMILY_SERVICE_CLIENT_INJECT_KEY, useValue: mockFamilyApi }],
    });

    service = module.get<FamilyGroupService>(FamilyGroupService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});

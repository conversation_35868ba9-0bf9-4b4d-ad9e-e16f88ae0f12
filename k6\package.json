{"name": "k6", "version": "1.0.0", "main": "index.js", "scripts": {"build:webhook": "esbuild webhook-load-tests.ts --bundle --platform=browser --format=esm --outfile=dist/webhook-load-tests.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "build:sequential-scenarios": "esbuild sequential-scenarios-test.ts --bundle --platform=browser --format=esm --outfile=dist/sequential-scenarios-test.js --target=es2017 --external:k6 --external:k6/http --external:k6/metrics --external:k6/options", "test:webhooks": "npm run build:webhook && dotenv -- k6 run dist/webhook-load-tests.js --env BASE_URL=http://localhost:7001", "test:sequential-scenarios": "npm run build:sequential-scenarios && dotenv -- k6 run dist/sequential-scenarios-test.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"k6": "^0.0.0"}, "devDependencies": {"@types/k6": "^1.0.1", "dotenv-cli": "^8.0.0", "esbuild": "^0.25.1", "typescript": "^5.8.2"}}
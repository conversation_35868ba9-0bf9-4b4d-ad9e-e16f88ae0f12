import axios, { AxiosResponse } from 'axios';
import chalk from 'chalk';
import qs from 'qs';

import { config } from './config';

export const FORWARDED_HOST = "kwsapi.v1staging.kidswebservices.com";

async function getWrapperAuthAppToken(key: "javiTest" | "domCraft"): Promise<string> {
  console.log(chalk.magenta('🔐 Requesting wrapper auth token for key ' + key));
  const response: AxiosResponse = await axios({
    method: 'POST',
    url: `${config.wrapperApi}/oauth/token`,
    data: qs.stringify({
      client_id: config.auth[key].wrapperClientId,
      client_secret: config.auth[key].wrapperClientSecret,
      grant_type: 'client_credentials',
      scope: 'app',
    }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      "x-forwarded-host": FORWARDED_HOST
    },
  });

  console.log(chalk.magenta('✅  Wrapper auth token received for key ' + key));
  return response.data.access_token;
}

async function getClassicAuthAppToken(key: "javiTest" | "domCraft"): Promise<string> {
  const username = config.auth[key].classicOauthUsername;
  const password = config.auth[key].classicOauthPassword;
  const token = Buffer.from(`${username}:${password}`).toString('base64');

  console.log(chalk.magenta('🔐 Requesting classic auth token for key ' + key));
  const response: AxiosResponse = await axios({
    method: 'POST',
    url: `${config.classicApi}/oauth/token`,
    data: qs.stringify({
      grant_type: 'client_credentials',
    }),
    headers: {
      Authorization: `Basic ${token}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  console.log(chalk.magenta('✅  Classic auth token received for key ' + key));
  return response.data.access_token;
}

async function getClassicAuthUserToken(key: "javiTest" | "domCraft", user: {
  username: string,
  password: string
}): Promise<string> {
  const username = config.auth[key].classicOauthUsername;
  const password = config.auth[key].classicOauthPassword;
  const token = Buffer.from(`${username}:${password}`).toString('base64');

  console.log(chalk.magenta('🔐🚹 Requesting classic auth user token for key ' + key + ` for user ${user.username}`));
  const response: AxiosResponse = await axios({
    method: 'POST',
    url: `${config.classicApi}/oauth/token`,
    data: qs.stringify({
      grant_type: 'password',
      username: user.username,
      password: user.password
    }),
    headers: {
      Authorization: `Basic ${token}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  console.log(chalk.magenta('✅  Classic auth token received for key ' + key));
  return response.data.access_token;
}

async function getWrapperAuthUserToken(key: "javiTest" | "domCraft", user: {
  username: string,
  password: string
}): Promise<string> {
  const username = config.auth[key].classicOauthUsername;
  const password = config.auth[key].classicOauthPassword;
  const token = Buffer.from(`${username}:${password}`).toString('base64');

  console.log(chalk.magenta('🔐🚹 Requesting wrapper auth user token for key ' + key + ` for user ${user.username}`));
  const response: AxiosResponse = await axios({
    method: 'POST',
    url: `${config.wrapperApi}/oauth/token`,
    data: qs.stringify({
      grant_type: 'password',
      username: user.username,
      password: user.password
    }),
    headers: {
      Authorization: `Basic ${token}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      "x-forwarded-host": FORWARDED_HOST
    },
  });

  console.log(chalk.magenta('✅  Wrapper auth token received for key ' + key));
  return response.data.access_token;
}

const tokenCacheMap = new Map<string, string>()

export async function getAuthToken(baseUrl: string, app: "javiTest" | "domCraft", user: {
  password: string;
  username: string
} | undefined): Promise<string> {
  const cacheKey = `${app}-${baseUrl}-${user?.username ?? ''}`;
  if (tokenCacheMap.has(cacheKey)) {
    return tokenCacheMap.get(cacheKey)!;
  }

  if (baseUrl === config.classicApi) {
    let accessToken;
    if (user) {
      accessToken = await getClassicAuthUserToken(app, user);
    } else {
      accessToken = await getClassicAuthAppToken(app);
    }
    tokenCacheMap.set(cacheKey, accessToken);
    return accessToken;
  } else {
    let accessToken;
    if (user) {
      accessToken = await getWrapperAuthUserToken(app, user);
    } else {
      accessToken = await getWrapperAuthAppToken(app);
    }
    tokenCacheMap.set(cacheKey, accessToken);
    return accessToken;
  }
}

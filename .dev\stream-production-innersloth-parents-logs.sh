#!/bin/bash

#<kube-context> - Required: The Kubernetes context to use
#<service-name> - Required: The name of the service whose pods you want to stream logs from
#[namespace] - Optional: The Kubernetes namespace (defaults to "default" if not provided)
#[tail-lines] - Optional: Number of log lines to show initially (defaults to 50 if not provided)

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

"$SCRIPT_DIR/stream-pod-logs.sh" PRODUCTION_kws_innersloth_us-east-1_0 sa-kws-parents-api-http kws-cluster-innersloth 500

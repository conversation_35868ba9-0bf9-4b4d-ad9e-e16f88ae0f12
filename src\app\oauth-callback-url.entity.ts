import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { App } from './app.entity';

@Entity()
export class OauthCallbackUrl {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => App, (app) => app.oauthCallbackUrls, { onDelete: 'CASCADE' })
  @JoinColumn([
    { name: 'appId', referencedColumnName: 'id' },
    { name: 'orgEnvId', referencedColumnName: 'orgEnvId' },
  ])
  app: App;

  @Column()
  appId: number;

  @Column({ type: 'character varying' })
  orgEnvId: string;

  @Column()
  url: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}

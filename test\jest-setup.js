// Jest setup file to capture console output and only show it for failed tests

const shouldFilterLog = (message) => {
  if (typeof message !== 'string') {
    return false;
  }

  return message.includes('Error closing statsDClient') ||
      message.includes('Error closing hot-shots socket') ||
      message.includes('ERR_SOCKET_DGRAM_NOT_RUNNING') ||
      message.includes('Response code 0, status: SystemError') ||
      message.includes('amplitude.service.ts');
};
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

if (process.env.SHOW_ALL_E2E_LOGS === 'false') {

  let currentTestLogs = [];
  let currentTestName = '';
  let testPassed = true;

  const captureConsoleOutput = (originalMethod, level) => {
    return (...args) => {
      if (args.length > 0 && shouldFilterLog(args[0])) {
        return;
    }

      currentTestLogs.push({
        level,
        args,
        timestamp: new Date().toISOString()
      });
    };
  };

  console.warn = captureConsoleOutput(originalConsoleWarn, 'WARN');
  console.error = captureConsoleOutput(originalConsoleError, 'ERROR');
  console.log = captureConsoleOutput(originalConsoleLog, 'LOG');

// Intercept Winston console logging
  try {
    const winston = require('winston');

    const ConsoleTransport = winston.transports.Console;
    if (ConsoleTransport && ConsoleTransport.prototype.log) {

      ConsoleTransport.prototype.log = function (info, callback) {
        if (info && info.message && shouldFilterLog(info.message)) {
          if (callback) {
            callback();
          }
          return;
        }

        currentTestLogs.push({
          level: (info.level || 'LOG').toUpperCase(),
          args: [info.message || JSON.stringify(info)],
          timestamp: new Date().toISOString()
        });

        if (callback) {
          callback();
        }
      };
    }
  } catch (error) {
    // Winston not available, continue with just console interception
  }

  beforeEach(() => {
    currentTestLogs = [];
    testPassed = true;

    const testState = expect.getState();
    currentTestName = testState.currentTestName || 'Unknown Test';
  });

  afterEach(() => {
    // If test failed, output all captured logs
    if (!testPassed && currentTestLogs.length > 0) {
      originalConsoleError(
          `\n📋 Console output for failed test: "${currentTestName}"`);
      originalConsoleError('='.repeat(80));

      currentTestLogs.forEach(log => {
        const prefix = `[${log.timestamp}] ${log.level}:`;
        switch (log.level) {
          case 'ERROR':
            originalConsoleError(prefix, ...log.args);
            break;
          case 'WARN':
            originalConsoleWarn(prefix, ...log.args);
            break;
          case 'LOG':
            originalConsoleLog(prefix, ...log.args);
            break;
        }
      });

      originalConsoleError('='.repeat(80));
      originalConsoleError(`End of console output for: "${currentTestName}"\n`);
    }

    currentTestLogs = [];
  });

// Hook into Jest's test result reporting to know when a test has failed
  const originalIt = global.it;
  global.it = (name, fn, timeout) => {
    return originalIt(name, async (...args) => {
      try {
        const result = await fn(...args);
        testPassed = true;
        return result;
      } catch (error) {
        testPassed = false;
        throw error;
      }
    }, timeout);
};

// Also handle test.each and other test variants
  const originalTest = global.test;
  global.test = (name, fn, timeout) => {
    return originalTest(name, async (...args) => {
      try {
        const result = await fn(...args);
        testPassed = true;
        return result;
      } catch (error) {
        testPassed = false;
        throw error;
      }
    }, timeout);
  };
} else {
  console.warn = (...args) => {
    if (args.length > 0 && shouldFilterLog(args[0])) {
      return;
    }
    originalConsoleWarn(...args);
  };
  console.error = (...args) => {
    if (args.length > 0 && shouldFilterLog(args[0])) {
      return;
    }
    originalConsoleError(...args);
  };
  console.log = (...args) => {
    if (args.length > 0 && shouldFilterLog(args[0])) {
      return;
    }
    originalConsoleLog(...args);
  };
}
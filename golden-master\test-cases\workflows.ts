import { EndpointConfig, WorkflowConfig } from '../config';
import { testData } from '../test-data';
import { createUserStatic } from '../test-utils';

export const workflowTests: (EndpointConfig | WorkflowConfig)[] = [
    {
        name: 'Delete activation user flow, single activation v2 endpoint (user account will be deleted)',
        workflow: [
            () => {
                return {
                    path: '/v2/apps/:appId/users',
                    method: 'POST',
                    params: {
                        appId: testData.apps.javiTest,
                    },
                    payload: {
                        country: 'GM',
                        language: 'en',
                        dateOfBirth: '2020-10-10',
                        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
                        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                };
            },
            ({ resBody }) => {
                return {
                    path: '/v2/apps/:appId/users/:userId',
                    method: 'DELETE',
                    params: {
                        userId: resBody.id,
                        appId: testData.apps.javiTest,
                    },
                };
            },
        ],
    },
    {
        name: 'Delete activation user flow, two activations v1',
        workflow: [
            () => {
                const newUser = createUserStatic(testData.apps.domCraft);
                return {
                    name: "Create new user",
                    path: '/v2/users',
                    method: 'POST',
                    payload: {
                        ...newUser,
                        token: "someTokenThatWeNeed"
                    },
                    headers: {
                        "x-kws-bypass-talon": "true"
                    },
                    bodyFieldConditions: {
                        id: "present"
                    },
                    workflowState: {
                        user: newUser
                    }
                };
            },
            ({ resBody, prevWorkflowState }) => {
                const user = {
                    ...prevWorkflowState.user,
                    id: resBody.id
                }
                return {
                    name: "Activate new user with javi-test",
                    path: '/v1/users/:userId/apps',
                    method: 'POST',
                    params: {
                        userId: user.id,
                    },
                    payload: {
                        appName: "javi-test",
                        permissions: ["Stranger.things", "Greece.Opt-out"],
                        dateOfBirth: "2010-01-01",
                        parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                    user: user,
                    useAuth: "javiTest",
                    workflowState: {
                        user: user
                    }
                };
            },
            ({ prevWorkflowState }) => {
                const user = prevWorkflowState.user;
                return {
                    name: "Activate new user with classic dom craft",
                    path: '/v1/users/:userId/apps',
                    method: 'POST',
                    params: {
                        userId: user.id,
                    },
                    payload: {
                        appName: "classicdomcraft",
                        permissions: ["permissionA"],
                        dateOfBirth: "2010-01-01",
                        parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                    user: user,
                    useAuth: "domCraft",
                };
            },
            ({ prevWorkflowState }) => {
                return {
                    path: '/v2/apps/:appId/users/:userId',
                    method: 'DELETE',
                    params: {
                        userId: prevWorkflowState.user.id,
                        appId: testData.apps.javiTest,
                    },
                };
            },
        ],
    },
    {
        name: 'Delete activation user flow, one activation v1, delete user account',
        workflow: [
            () => {
                const newUser = createUserStatic(testData.apps.domCraft);
                return {
                    name: "Create new user",
                    path: '/v2/users',
                    method: 'POST',
                    payload: {
                        ...newUser,
                        token: "someTokenThatWeNeed"
                    },
                    headers: {
                        "x-kws-bypass-talon": "true"
                    },
                    bodyFieldConditions: {
                        id: "present"
                    },
                    workflowState: {
                        user: newUser
                    }
                };
            },
            ({ resBody, prevWorkflowState }) => {
                const user = {
                    ...prevWorkflowState.user,
                    id: resBody.id
                }
                return {
                    name: "Activate new user with javi-test",
                    path: '/v1/users/:userId/apps',
                    method: 'POST',
                    params: {
                        userId: user.id,
                    },
                    payload: {
                        appName: "javi-test",
                        permissions: ["Stranger.things", "Greece.Opt-out"],
                        dateOfBirth: "2010-01-01",
                        parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                    user: user,
                    useAuth: "javiTest",
                    workflowState: {
                        user: user
                    }
                };
            },
            ({ prevWorkflowState }) => {
                return {
                    path: '/v2/apps/:appId/users/:userId',
                    method: 'DELETE',
                    params: {
                        userId: prevWorkflowState.user.id,
                        appId: testData.apps.javiTest,
                    },
                };
            },
        ],
    },
    {
        name: 'Activate app for new user, two activations',
        workflow: [
            () => {
                const newUser = createUserStatic(testData.apps.domCraft);
                return {
                    name: "Create new user",
                    path: '/v2/users',
                    method: 'POST',
                    payload: {
                        ...newUser,
                        token: "someTokenThatWeNeed"
                    },
                    headers: {
                        "x-kws-bypass-talon": "true"
                    },
                    bodyFieldConditions: {
                        id: "present"
                    },
                    workflowState: {
                        user: newUser
                    }
                };
            },
            ({ resBody, prevWorkflowState }) => {
                const user = {
                    ...prevWorkflowState.user,
                    id: resBody.id
                }
                return {
                    name: "Activate new user with javi-test",
                    path: '/v1/users/:userId/apps',
                    method: 'POST',
                    params: {
                        userId: user.id,
                    },
                    payload: {
                        appName: "javi-test",
                        permissions: ["Stranger.things", "Greece.Opt-out"],
                        dateOfBirth: "2010-01-01",
                        parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                    user: user,
                    useAuth: "javiTest",
                    workflowState: {
                        user: user
                    }
                };
            },
            ({ prevWorkflowState }) => {
                const user = prevWorkflowState.user;
                return {
                    name: "Activate new user with classic dom craft",
                    path: '/v1/users/:userId/apps',
                    method: 'POST',
                    params: {
                        userId: user.id,
                    },
                    payload: {
                        appName: "classicdomcraft",
                        permissions: ["permissionA"],
                        dateOfBirth: "2010-01-01",
                        parentEmail: "dominic.wild+activate-user-v1-{random}@xa.epicgames.com"
                    },
                    bodyFieldConditions: {
                        id: 'skip',
                        uuid: 'skip',
                    },
                    user: user,
                    useAuth: "domCraft",
                };
            },
        ],
    },
    {
        name: 'Create user and activate with a second app v2',
        workflow: [
            () => {
                return {
                    name: 'Create user',
                    path: '/v2/apps/:appId/users',
                    method: 'POST',
                    params: {
                        appId: testData.apps.javiTest,
                    },
                    payload: {
                        country: 'GM',
                        language: 'en',
                        dateOfBirth: '2020-10-10',
                        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
                        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
                    },
                    bodyFieldConditions: {
                        id: 'present',
                        uuid: 'skip',
                    },
                };
            },
            ({ resBody }) => {
                return {
                    name: "Activating user with another app v2 endpoint",
                    path: '/v2/apps/:appId/users/:userId/activate',
                    method: 'POST',
                    useAuth: "domCraft",
                    params: {
                        userId: resBody.id,
                        appId: testData.apps.domCraft,
                    },
                    payload: {
                        permissions: ["permissionA"]
                    },
                    bodyFieldConditions: {
                        id: 'present',
                    },
                };
            },
        ],
    },
    {
        name: 'Request permissions with user that does not have activation for that app',
        workflow: [
            () => {
                return {
                    name: 'Create user',
                    path: '/v2/apps/:appId/users',
                    method: 'POST',
                    params: {
                        appId: testData.apps.javiTest,
                    },
                    payload: {
                        country: 'GM',
                        language: 'en',
                        dateOfBirth: '2020-10-10',
                        parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
                        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
                    },
                    bodyFieldConditions: {
                        id: 'present',
                        uuid: 'skip',
                    },
                };
            },
            ({ resBody }) => {
                return {
                    name: "Request permissions with user that does not have activation for that app",
                    path: '/v2/apps/:appId/users/:userId/request-permissions',
                    method: 'POST',
                    useAuth: "domCraft",
                    params: {
                        userId: resBody.id,
                        appId: testData.apps.domCraft,
                    },
                    payload: {
                        dateOfBirth: '2016-09-14',
                        permissions: ["permissionA"]
                    },
                    bodyFieldConditions: {
                        errorMessage: 'present',
                    },
                };
            },
        ],
    }
]; 
import axios, { AxiosResponse } from 'axios';
import chalk from 'chalk';
import { get, isArray, omit, set } from 'lodash';
import qs from 'qs';

import { BodyFieldConditions, config, EndpointConfig } from './config';
import { FORWARDED_HOST, getAuthToken } from './get-auth-token';
import { formatDiff } from './log-utils';

export type RawResponse = {
  status: number;
  headers: Partial<Record<string, string>>;
  data: any | string;
  host: string;
};

function processPayload(payload: Record<string, any>) {
  const payloadCopy = { ...payload };
  for (const key of Object.keys(payloadCopy)) {
    if (typeof payloadCopy[key] === 'string' && payloadCopy[key].includes('{random}')) {
      payloadCopy[key] = payloadCopy[key].replace('{random}', Math.random().toString(36).slice(2, 15));
    }
    if (typeof payloadCopy[key] === 'object') {
      processPayload(payloadCopy[key]);
    }
  }
  return payloadCopy;
}

export async function makeRequest(
  baseUrl: string,
  endpoint: EndpointConfig,
): Promise<
  AxiosResponse & {
    host: string;
  }
> {
  const defaultParams = config.defaultParams;
  const url = endpoint.path.replaceAll(/:([^/]+)/g, (_, param) => {
    return endpoint.params?.[param] || defaultParams?.[param] || '';
  });

  const payload = processPayload(endpoint.payload);

  const contentType = endpoint.contentType || 'application/json';
  let requestData: any;

  if (contentType === 'application/x-www-form-urlencoded') {
    requestData = qs.stringify(payload);
  } else {
    requestData = payload;
  }

  const headers: Record<string, string> = {
    'Content-Type': contentType,
    'x-forwarded-host': FORWARDED_HOST,
    ...endpoint.headers,
  };

  if (!endpoint.skipAuth) {
    headers.Authorization = `Bearer ${await getAuthToken(baseUrl, endpoint.useAuth ?? 'javiTest', endpoint.user)}`;
  }

  const response = await axios({
    method: endpoint.method,
    url: `${baseUrl}${url}`,
    data: requestData,
    headers,
    validateStatus: () => true,
  });
  return { ...response, host: response.request.host };
}

export type ResponseResult = ReturnType<typeof sanitizeResponse>;

export function sanitizeResponse(
  response: RawResponse,
  bodyFieldConditions: BodyFieldConditions = {},
  skipHeaders: string[] = [],
  sortKey?: string,
) {
  const headersToAlwaysIgnore = [
    'content-length',
    'date',
    'cache-control',
    'pragma',
    'expires',
    'x-epic-correlation-id',
    'etag',
    'x-request-id',
    'x-powered-by',
    'keep-alive',
    'connection',
    "x-ratelimit-limit",
    "x-ratelimit-remaining",
    "x-ratelimit-reset",
    "access-control-allow-origin"
  ];
  let data: string | Record<string, any> | [Record<string, any>] = { ...response.data };
  if (isArray(response.data)) {
    data = [...response.data]
  }
  try {
    if (typeof data === 'string') {
      data = JSON.parse(data) as object;
    }

    if (sortKey && Array.isArray(data)) {
      data = data.sort((a, b) => a[sortKey].localeCompare(b[sortKey]));
    }

    const skipFields = Object.keys(bodyFieldConditions).filter((key) => bodyFieldConditions[key] === 'skip');
    if (skipFields.length > 0) {
      data = omit(data, skipFields); // This converts arrays to objects and can break array comparisons if not sorted
    }

    const isPresentFields = Object.keys(bodyFieldConditions).filter((key) => bodyFieldConditions[key] === 'present');
    for (const key of isPresentFields) {
      const valueInBody = get(data, key);
      if (valueInBody === undefined) {
        console.log(chalk.yellow(`🔔 Key ${key} not found in body when processing "isPresent" condition`));
      } else {
        set(data, key, 'isPresentComparisonValue');
      }
    }

    if (data && typeof data === 'object' && !Array.isArray(data)) {
      const obj = data as Record<string, unknown>;
      for (const key of Object.keys(obj)) {
        const val = obj[key];
        if (Array.isArray(val) && val.length > 0) {
          (val as any[]).sort();
        }
      }
    }

    const isClassicApiResponse = config.classicApi.includes(response.host);
    if (isClassicApiResponse) {
      const isRedactedFields = Object.keys(bodyFieldConditions).filter(
        (key) => bodyFieldConditions[key] === 'redacted',
      );
      for (const key of isRedactedFields) {
        const valueInBody = get(data, key);
        if (valueInBody === undefined) {
          console.log(chalk.yellow(`🔔 Key ${key} not found in body when processing "redacted" condition`));
        } else {
          set(data, key, '[REDACTED]');
        }
      }
    }
  } catch {
    // Do nothing
  }
  return {
    status: response.status,
    headers: omit(response.headers, [...headersToAlwaysIgnore, ...skipHeaders]),
    body: data,
  };
}

export async function compareResponses(sourceSanitized: ResponseResult, targetSanitized: ResponseResult) {
  try {
    const diffString = formatDiff(sourceSanitized, targetSanitized);

    if (diffString.includes('Compared values have no visual difference.')) {
      console.log(chalk.green('✔ Responses match\n'));
      if (config.logFullResponses) {
        console.log('Full responses after sanitization:');
        console.log('Classic', JSON.stringify(sourceSanitized, null, 2));
        console.log('Wrapper', JSON.stringify(targetSanitized, null, 2));
      }
      return true;
    } else {
      console.log(chalk.red('❌  Responses differ'));
      console.log(chalk.yellow('🔍 Differences:'));
      console.log(formatDiff(sourceSanitized, targetSanitized));
      console.log('\n');
      if (config.logFullResponses) {
        console.log('Full responses after sanitization:');
        console.log('Classic', JSON.stringify(sourceSanitized, null, 2));
        console.log('Wrapper', JSON.stringify(targetSanitized, null, 2));
      }
      return false;
    }
  } catch (error) {
    console.log(chalk.red(`🐛 Error comparing responses: ${error.message}\n`));
    return false;
  }
}

export function createUserStatic(appId: string) {
  const randomString = Math.random().toString(36).slice(2, 15);
  return {
    username: `golden-master-${randomString}`,
    password: "goodPassword42",
    originAppId: +appId,
    dateOfBirth: "2022-05-13",
    parentEmail: `dominic.wild+${randomString}@xa.epicgames.com`,
  }
}



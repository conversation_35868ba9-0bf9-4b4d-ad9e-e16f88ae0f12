import { BadRequestException, NotFoundException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  AppUser,
  ResultPermission,
  UserCreateDTO,
  UserCreateResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { plainToInstance } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

describe('AppController', () => {
  let controller: AppController;
  let appService: AppService;
  let userService: UserService;
  const testOrgEnv = { id: uuidv4() } as OrgEnv;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      controllers: [AppController],
    });

    controller = module.get<AppController>(AppController);
    appService = module.get<AppService>(AppService);
    userService = module.get<UserService>(UserService);
  });

  describe('createUser', () => {
    it('should return mapped response without extra props', async () => {
      jest.spyOn(appService, 'registerUser').mockResolvedValueOnce({
        id: 1,
        uuid: '00000000-0000-0000-0000-000000000000',
        isMinor: true,
        permissions: {
          'chat.voice': true,
        },
        extraProp: 42,
      } as UserCreateResponseDTO);

      await expect(
        controller.createUser(
          1,
          {
            country: 'AF',
            dateOfBirth: '2010-01-01',
            language: 'ca',
            parentEmail: '<EMAIL>',
          },
          testOrgEnv,
        ),
      ).resolves.toEqual({
        id: 1,
        isMinor: true,
        permissions: {
          'chat.voice': true,
        },
        uuid: '00000000-0000-0000-0000-000000000000',
      });
    });

    it('should allow undefined dateOfBirth for optional customers', async () => {
      jest.spyOn(appService, 'registerUser').mockResolvedValueOnce({
        id: 1,
        uuid: '00000000-0000-0000-0000-000000000000',
        dateOfBirth: void 0, // undefined dateOfBirth
        isMinor: true,
        permissions: {},
      } as UserCreateResponseDTO);

      const optionalOrgEnv = { id: 'b56a1ed8-f0ad-4d44-9344-87bedbe4211d' } as OrgEnv; // beme-health

      await expect(
        controller.createUser(
          232793602, // beme-health app
          {
            country: 'US',
            // dateOfBirth not provided (undefined) should be allowed for optional customers
            language: 'en',
            parentEmail: '<EMAIL>',
          } as UserCreateDTO,
          optionalOrgEnv,
        ),
      ).resolves.toEqual({
        id: 1,
        isMinor: true,
        permissions: {},
        uuid: '00000000-0000-0000-0000-000000000000',
      });
    });

    it('should validate dateOfBirth format for optional customers when provided', async () => {
      const optionalOrgEnv = { id: 'b56a1ed8-f0ad-4d44-9344-87bedbe4211d' } as OrgEnv; // beme-health

      await expect(
        controller.createUser(
          232793602, // beme-health app
          {
            country: 'US',
            dateOfBirth: 'invalid-date', // Invalid format should still throw error
            language: 'en',
            parentEmail: '<EMAIL>',
          },
          optionalOrgEnv,
        ),
      ).rejects.toThrow(new BadRequestException('dateOfBirth is required and must be in YYYY-MM-DD format'));
    });

    it('should require dateOfBirth for non-optional customers', async () => {
      const normalOrgEnv = { id: 'some-other-org-env-id' } as OrgEnv; // Not in optional list

      await expect(
        controller.createUser(
          12345, // Any app
          {
            country: 'US',
            // dateOfBirth not provided should throw error for non-optional customers
            language: 'en',
            parentEmail: '<EMAIL>',
          } as UserCreateDTO,
          normalOrgEnv,
        ),
      ).rejects.toThrow(new BadRequestException('dateOfBirth is required and must be in YYYY-MM-DD format'));
    });
  });

  describe('updateUserPermissions', () => {
    it('should handle empty permissions by returning user data', async () => {
      const mockUser = {
        id: 1,
        dateOfBirth: '10-10-2012',
        language: 'en',
        signUpCountry: 'us',
        username: 'something',
      } as unknown as User;
      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(mockUser);

      const result = await controller.updateUserPermissions(
        {
          appId: 1,
          userId: 1,
        },
        { permissions: [] },
        testOrgEnv,
      );

      const appUser = plainToInstance(AppUser, {
        id: 1,
        dateOfBirth: '10-10-2012',
        isDeleted: false,
        language: 'en',
        parentDeleted: false,
        parentEmail: undefined,
        parentExpired: false,
        parentIdVerified: false,
        parentRejected: false,
        parentVerified: false,
        signUpCountry: 'us',
        username: 'something',
      });

      expect(result).toEqual({
        user: appUser,
        permissions: undefined,
        resultPermissions: undefined,
      });
    });

    it('should throw NotFoundException when user not found and no permissions provided', async () => {
      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(null);

      await expect(
        controller.updateUserPermissions(
          {
            appId: 1,
            userId: 1,
          },
          { permissions: [] },
          testOrgEnv,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should attempt to activate user for Innersloth org env', async () => {
      const innerslothOrgEnv = { id: '8080e2b3-4f65-47b7-bd67-df01913a9a0d' } as OrgEnv;

      const mockUser = {
        id: 1,
        dateOfBirth: '10-10-2012',
        language: 'en',
        signUpCountry: 'us',
        username: 'something',
      } as unknown as User;

      jest.spyOn(userService, 'getUser').mockResolvedValueOnce(mockUser);
      const userActivatedSpy = jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      const activateUserSpy = jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: mockUser,
        activationPermissions: {},
      });

      await controller.updateUserPermissions(
        {
          appId: 1,
          userId: 1,
        },
        { permissions: [] },
        innerslothOrgEnv,
      );

      expect(userActivatedSpy).toHaveBeenCalledWith(1, 1, innerslothOrgEnv.id);
      expect(activateUserSpy).toHaveBeenCalledWith(1, 1, { permissions: [] }, innerslothOrgEnv.id);
    });

    it('should throw NotFoundException when user is not activated for provided app and permissions are provided', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);

      await expect(
        controller.updateUserPermissions(
          {
            appId: 1,
            userId: 1,
          },
          { permissions: ['somePermission'] },
          testOrgEnv,
        ),
      ).rejects.toThrow(new NotFoundException('User not activated for the provided app'));

      expect(userService.userActivatedForApp).toHaveBeenCalledWith(1, 1, testOrgEnv.id);
    });

    it('should return processed permissions data when permissions provided', async () => {
      const mockResponse = {
        user: {
          id: 1,
          language: 'en',
          parentEmail: '<EMAIL>',
          dateOfBirth: undefined,
          isDeleted: false,
          parentDeleted: false,
          parentExpired: false,
          parentIdVerified: false,
          parentRejected: false,
          parentVerified: false,
          signUpCountry: undefined,
          username: undefined,
        },
        userSettings: [{ namespace: 'default', settingName: 'permission1' }],
      } as unknown as ReturnType<typeof appService.requestPermissionsForUser>;

      const resultPermission: ResultPermission = {
        description: 'someDescription',
        displayName: 'someDisplayName',
        name: 'someName',
      };
      jest.spyOn(appService, 'requestPermissionsForUser').mockResolvedValueOnce(mockResponse);
      jest.spyOn(SettingsService, 'removeTermsAndConditionsSetting').mockReturnValueOnce([
        {
          namespace: 'permission1',
          settingName: 'granted',
        },
      ]);
      jest.spyOn(SettingsService, 'transformSettingsToGroupedPermissions').mockReturnValueOnce({
        alreadyGrantedPerms: [resultPermission],
        automaticallyGrantedPerms: [resultPermission],
        missingPermissions: [resultPermission],
      });
      jest.spyOn(SettingsService, 'transformSettingsToPermissions').mockReturnValueOnce({
        permission1: true,
      });
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(true);

      const result = await controller.updateUserPermissions(
        { appId: 1, userId: 1 },
        { permissions: ['permission1'] },
        testOrgEnv,
      );

      expect(result).toEqual({
        user: {
          id: 1,
          language: 'en',
          parentEmail: '[REDACTED]',
          dateOfBirth: undefined,
          isDeleted: false,
          parentDeleted: false,
          parentExpired: false,
          parentIdVerified: false,
          parentRejected: false,
          parentVerified: false,
          signUpCountry: undefined,
          username: undefined,
        },
        permissions: { permission1: true },
        resultPermissions: {
          alreadyGrantedPerms: [resultPermission],
          automaticallyGrantedPerms: [resultPermission],
          missingPermissions: [resultPermission],
        },
      });

      expect(appService.requestPermissionsForUser).toHaveBeenCalledWith(testOrgEnv.id, {
        appId: 1,
        userId: 1,
        permissions: ['permission1'],
      });
    });
  });

  describe('updateParentEmail', () => {
    it('should return void and call service', async () => {
      const spy = jest.spyOn(appService, 'updateParentEmail');
      await expect(
        controller.updateParentEmail({ appId: 1, userId: 1 }, { parentEmail: 'email' }, testOrgEnv),
      ).resolves.toBeUndefined();
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('getUser', () => {
    it('should return expected response', async () => {
      const spyGetUser = jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: 1,
        language: 'en',
        permissions: {},
        username: null,
        displayName: null,
        appData: [],
        activationCreatedAt: new Date('2024-10-10'),
        createdAt: new Date('2024-10-10'),
        consentAgeForCountry: 16,
        isMinor: true,
        parentEmail: '[REDACTED]',
        parentState: {
          verified: true,
          expired: false,
          rejected: false,
          idVerified: true,
          deleted: false,
        },
      });
      await expect(controller.getUser({ appId: 1, userId: 1 }, testOrgEnv)).resolves.toEqual({
        activationCreatedAt: '2024-10-10T00:00:00.000Z',
        appData: [],
        city: null,
        consentAgeForCountry: 16,
        createdAt: '2024-10-10T00:00:00.000Z',
        displayName: null,
        id: 1,
        isMinor: true,
        language: 'en',
        parentEmail: '[REDACTED]',
        parentState: {
          deleted: false,
          expired: false,
          idVerified: true,
          rejected: false,
          verified: true,
        },
        country: null,
        permissions: {},
        dateOfBirth: undefined,
        email: null,
        firstName: null,
        lastName: null,
        postalCode: null,
        streetAddress: null,
        username: null,
      });
      expect(spyGetUser).toHaveBeenCalled();
    });
  });

  describe('getUserPermissions', () => {
    it('should return permissions', async () => {
      const mockDate = new Date();
      const userId = 1;
      const appId = 2;
      const mockPermissions = {
        perm1: true,
        perm2: false,
      };

      jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: userId,
        isMinor: false,
        language: 'en',
        activationCreatedAt: mockDate,
        createdAt: mockDate,
        consentAgeForCountry: 18,
        permissions: mockPermissions,
      });

      const res = await controller.getUserPermissions('', appId, userId, testOrgEnv);

      expect(res).toEqual(mockPermissions);
    });

    it('should return extended permissions', async () => {
      const mockDate = new Date();
      const userId = 1;
      const appId = 2;
      const mockPermissions = {
        perm1: true,
        perm2: false,
      };
      const isMinor = false;

      jest.spyOn(appService, 'getUser').mockResolvedValueOnce({
        id: userId,
        isMinor: isMinor,
        language: 'en',
        activationCreatedAt: mockDate,
        createdAt: mockDate,
        consentAgeForCountry: 18,
        permissions: mockPermissions,
      });

      const res = await controller.getUserPermissions('true', appId, userId, testOrgEnv);

      expect(res).toEqual({
        permissions: mockPermissions,
        isGraduated: !isMinor,
        disabledPermissions: [],
      });
    });
  });

  describe('deleteUserSettings', () => {
    it('throws NotFound when user has no activation', async () => {
      const hasActivationsSpy = jest.spyOn(appService, 'userHasActivation').mockResolvedValueOnce(false);

      await expect(
        controller.deleteUserActivation(
          {
            appId: 1,
            userId: 1,
          },
          testOrgEnv,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(hasActivationsSpy).toHaveBeenCalled();
    });

    it('calls deleteUserSettings when user has activation', async () => {
      const hasActivationsSpy = jest.spyOn(appService, 'userHasActivation').mockResolvedValueOnce(true);
      const deleteUserSettingsSpy = jest.spyOn(userService, 'deleteActivation').mockResolvedValueOnce();

      await expect(
        controller.deleteUserActivation(
          {
            appId: 1,
            userId: 1,
          },
          testOrgEnv,
        ),
      ).resolves.toBe(undefined);

      expect(hasActivationsSpy).toHaveBeenCalled();
      expect(deleteUserSettingsSpy).toHaveBeenCalled();
    });
  });

  describe('reviewPermissions should', () => {
    it('return no content', async () => {
      const reviewPermissionsSpy = jest.spyOn(appService, 'reviewPermissions').mockResolvedValueOnce();

      const result = await controller.reviewPermissions({ appId: 1, userId: 1 }, testOrgEnv);

      expect(result).toEqual({});
      expect(reviewPermissionsSpy).toHaveBeenCalled();
    });

    it('generate a consent request', async () => {
      const sendGenerateConsentRequest = jest.fn().mockResolvedValueOnce({});
      appService.reviewPermissions = sendGenerateConsentRequest;

      await controller.reviewPermissions({ appId: 1, userId: 1 }, testOrgEnv);

      expect(sendGenerateConsentRequest).toHaveBeenCalled();
    });
  });

  describe('activeUserToAnotherApp', () => {
    const mockUserId = 1;
    const mockAppId = 2;
    const mockOrgEnv = { id: 'test-org-env' } as OrgEnv;
    const mockPermissions = ['chat.voice', 'chat.text'];

    it('should successfully activate user to another app', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: { id: mockUserId } as unknown as User,
        activationPermissions: {
          'chat.voice': true,
          'chat.text': false,
        },
      });

      const result = await controller.activeUserToAnotherApp(
        mockAppId,
        mockUserId,
        { permissions: mockPermissions },
        mockOrgEnv,
      );

      expect(result).toEqual({
        id: mockUserId,
        permissions: {
          'chat.voice': true,
          'chat.text': false,
        },
      });
      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(userService.activateUserToApp).toHaveBeenCalledWith(
        mockUserId,
        mockAppId,
        { permissions: mockPermissions },
        mockOrgEnv.id,
      );
    });

    it('should throw ConflictException when user is already activated for the app', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(true);
      const activateUserToAppSpy = jest.spyOn(userService, 'activateUserToApp');

      await expect(
        controller.activeUserToAnotherApp(mockAppId, mockUserId, { permissions: mockPermissions }, mockOrgEnv),
      ).rejects.toThrow('App already activated');

      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(activateUserToAppSpy).not.toHaveBeenCalled();
    });

    it('should handle activation without permissions', async () => {
      jest.spyOn(userService, 'userActivatedForApp').mockResolvedValueOnce(false);
      jest.spyOn(userService, 'activateUserToApp').mockResolvedValueOnce({
        user: { id: mockUserId } as unknown as User,
        activationPermissions: {},
      });

      const result = await controller.activeUserToAnotherApp(mockAppId, mockUserId, { permissions: [] }, mockOrgEnv);

      expect(result).toEqual({
        id: mockUserId,
        permissions: {},
      });
      expect(userService.userActivatedForApp).toHaveBeenCalledWith(mockUserId, mockAppId, mockOrgEnv.id);
      expect(userService.activateUserToApp).toHaveBeenCalledWith(
        mockUserId,
        mockAppId,
        { permissions: [] },
        mockOrgEnv.id,
      );
    });
  });

  describe('getTranslatedPermissions', () => {
    const mockAppId = 123;
    const mockTranslatedPermissions = [
      {
        name: 'chat.voice',
        displayName: 'Voice Chat',
        childFacingDescription: 'Allow voice chat',
        privacyNotice: 'Voice chat privacy notice',
      },
      {
        name: 'chat.text',
        displayName: 'Text Chat',
        childFacingDescription: 'Allow text chat',
        privacyNotice: 'Text chat privacy notice',
      },
    ];

    it('should return translated permissions with default country code when no country header provided', async () => {
      jest.spyOn(appService, 'getTranslatedPermissionsForApp').mockResolvedValueOnce(mockTranslatedPermissions);

      const result = await controller.getTranslatedPermissions(mockAppId, 'chat.voice,chat.text', testOrgEnv, 'en-US');

      expect(result).toEqual(mockTranslatedPermissions);
      expect(appService.getTranslatedPermissionsForApp).toHaveBeenCalledWith(mockAppId, testOrgEnv, 'ZZ', 'en-US', [
        'chat.voice',
        'chat.text',
      ]);
    });

    it('should convert country header to uppercase', async () => {
      jest.spyOn(appService, 'getTranslatedPermissionsForApp').mockResolvedValueOnce(mockTranslatedPermissions);

      await controller.getTranslatedPermissions(mockAppId, 'chat.voice', testOrgEnv, 'en-US', 'us');

      expect(appService.getTranslatedPermissionsForApp).toHaveBeenCalledWith(mockAppId, testOrgEnv, 'US', 'en-US', [
        'chat.voice',
      ]);
    });

    it('should handle empty permissions parameter', async () => {
      jest.spyOn(appService, 'getTranslatedPermissionsForApp').mockResolvedValueOnce(mockTranslatedPermissions);

      await controller.getTranslatedPermissions(mockAppId, undefined, testOrgEnv, 'en-US', 'GB');

      expect(appService.getTranslatedPermissionsForApp).toHaveBeenCalledWith(mockAppId, testOrgEnv, 'GB', 'en-US', []);
    });

    it('should handle missing accept-language header', async () => {
      jest.spyOn(appService, 'getTranslatedPermissionsForApp').mockResolvedValueOnce(mockTranslatedPermissions);

      await controller.getTranslatedPermissions(mockAppId, 'chat.voice', testOrgEnv, undefined, 'FR');

      expect(appService.getTranslatedPermissionsForApp).toHaveBeenCalledWith(mockAppId, testOrgEnv, 'FR', undefined, [
        'chat.voice',
      ]);
    });

    it('should handle single permission in parameter', async () => {
      jest.spyOn(appService, 'getTranslatedPermissionsForApp').mockResolvedValueOnce([mockTranslatedPermissions[0]]);

      const result = await controller.getTranslatedPermissions(mockAppId, 'chat.voice', testOrgEnv, 'en-US', 'CA');

      expect(result).toEqual([mockTranslatedPermissions[0]]);
      expect(appService.getTranslatedPermissionsForApp).toHaveBeenCalledWith(mockAppId, testOrgEnv, 'CA', 'en-US', [
        'chat.voice',
      ]);
    });
  });

  describe('getAppConfig', () => {
    const mockAppConfig = {
      app: {
        id: 123,
        name: 'Test App',
        oauthClientId: 'test-client-id',
        termsAndConditionsRequired: false,
        mainContainerBgColour: null,
        oauthImplicitEnabled: false,
        parentEmailRequired: true,
        areAnonAccountsEnabled: false,
        verificationRequired: false,
        displayNameRequired: true,
        oauthCallbackUrls: ['https://example.com/callback'],
        defaultOauthCallbackUrl: null,
        logoUrl: { en: 'https://example.com/logo.png' },
        iconUrl: { en: 'https://example.com/icon.png' },
      },
      languages: ['en'],
      identityName: 'Niantic Kids',
    };

    it('should call appService.getAppConfig with correct parameters', async () => {
      jest.spyOn(appService, 'getAppConfig').mockResolvedValueOnce(mockAppConfig);

      const result = await controller.getAppConfig(
        { oauthClientId: 'test-client-id', redirectUri: 'https://example.com/callback' },
        testOrgEnv,
      );

      expect(appService.getAppConfig).toHaveBeenCalledWith('test-client-id', testOrgEnv.id);
      expect(result).toEqual(mockAppConfig);
    });

    it('should handle missing redirectUri parameter', async () => {
      jest.spyOn(appService, 'getAppConfig').mockResolvedValueOnce(mockAppConfig);

      const result = await controller.getAppConfig({ oauthClientId: 'test-client-id' }, testOrgEnv);

      expect(appService.getAppConfig).toHaveBeenCalledWith('test-client-id', testOrgEnv.id);
      expect(result).toEqual(mockAppConfig);
    });
  });
});

import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable, paramsExtractor } from '@superawesome/freekws-cache-decorator';
import { SALogger } from '@superawesome/freekws-common-logger';
import { EMemberRole } from '@superawesome/freekws-family-service-common';
import { Producer } from '@superawesome/freekws-kafka';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import {
  ClassicWebhookDTO,
  FamiliesGroupDeletedPayloadDto,
  FamiliesGuardianRequestExpiredPayloadDto,
  FamiliesUserAddedToFamilyDTO,
  SettingsEffectiveValueChangedDTO,
  SettingsEffectiveValuesChangedPayloadDTO,
} from '@superawesome/freekws-queue-messages/webhook';
import { ClassicWebhookName } from '@superawesome/freekws-queue-messages/webhook/classic-webhook.dto';
import { ChildAccountGraduatedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-account-graduated.payload.dto';
import { ChildActivatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-activated.payload.dto';
import { ChildActivationDeletedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-activation-deleted.payload.dto';
import { ChildLinkedToParentPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-linked-to-parent.payload.dto';
import { ParentAccountDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/parent-account-deleted.payload.dto';
import { ParentUnlinkedFromChildPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/parent-unlinked-from-child.payload.dto';
import { UnresponsiveParentAccountDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/unresponsive-parent-account-deleted.payload.dto';
import { UserAccountDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/user-account-deleted.payload.dto';
import { UserPermissionChangedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/user-permission-changed.payload.dto';
import { FamiliesUserRemovedFromFamilyDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/families-user-removed-from-family.payload.dto';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { Span } from 'nestjs-ddtrace';
import { Repository } from 'typeorm';

import { AppLevelWebhookPayload, OrgLevelWebhookPayload } from './types';
import { Webhook } from './webhook.entity';
import { ConfigService } from '../common/services/config/config.service';
import { KAFKA_PRODUCER } from '../common/services/kafka/kafka-producer.module';
import { DEFAULT_T_AND_C_PERMISSION_NAME, SettingsService } from '../common/services/settings/settings.service';
import { OrgEnv } from '../org-env/org-env.entity';
import { User } from '../user/user.entity';

@Injectable()
@Span()
export class WebhookService {
  private readonly TOPIC: string;
  private readonly logger: SALogger;

  constructor(
    @InjectRepository(Webhook) private readonly webhooksRepository: Repository<Webhook>,
    @InjectRepository(User) private readonly usersRepository: Repository<User>,
    @InjectRepository(OrgEnv) private readonly orgEnvRepo: Repository<OrgEnv>,
    @Inject(KAFKA_PRODUCER) private readonly kafkaProducer: Producer,
    private readonly config: ConfigService,
    private readonly metrics: MetricsService,
    private readonly settingsService: SettingsService,
  ) {
    this.TOPIC = this.config.getAppWebhooksConfig().topicName;
    this.logger = new SALogger();
  }

  async sendChildActivated(payload: ChildActivatedPayloadDTO, appId: number, orgEnvId: string): Promise<number | void> {
    return await this.sendAppLevelClassicWebhookWithAppId(ClassicWebhookName.CHILD_ACTIVATED, appId, payload, orgEnvId);
  }

  async sendChildActivationDeleted(
    payload: ChildActivationDeletedPayloadDTO,
    appId: number,
    orgEnvId: string,
  ): Promise<number | void> {
    return await this.sendAppLevelClassicWebhookWithAppId(
      ClassicWebhookName.CHILD_ACTIVATION_DELETED,
      appId,
      payload,
      orgEnvId,
    );
  }

  async sendUserAccountDeleted(payload: UserAccountDeletedPayloadDto, orgId: string): Promise<number | void> {
    return this.sendOrgLevelClassicWebhook(ClassicWebhookName.USER_ACCOUNT_DELETED, orgId, payload);
  }

  async sendAccountGraduated(
    freeKwsWebhookPayload: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO>,
  ): Promise<number | void> {
    const payload: ChildAccountGraduatedPayloadDto = {
      userId: Number(freeKwsWebhookPayload.payload.userId),
    };

    return this.sendOrgLevelClassicWebhook(
      ClassicWebhookName.CHILD_ACCOUNT_GRADUATED,
      freeKwsWebhookPayload.orgId,
      payload,
    );
  }

  async sendUserAddedToFamily(freeKwsWebhookPayload: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO>) {
    const payload: ChildLinkedToParentPayloadDTO = {
      userId: Number(freeKwsWebhookPayload.payload.userId),
      // Parent id is intentionally set to userId to match Classic behaviour and as it is irrelevant for the customer
      parentId: Number(freeKwsWebhookPayload.payload.userId),
    };

    return this.sendOrgLevelClassicWebhook(
      ClassicWebhookName.CHILD_LINKED_TO_PARENT,
      freeKwsWebhookPayload.orgId,
      payload,
    );
  }

  async sendUserRemovedFromFamily(freeKwsWebhookPayload: OrgLevelWebhookPayload<FamiliesUserRemovedFromFamilyDTO>) {
    const payload: ParentUnlinkedFromChildPayloadDto = {
      userId: Number(freeKwsWebhookPayload.payload.userId),
    };

    return this.sendOrgLevelClassicWebhook(
      ClassicWebhookName.PARENT_UNLINKED_FROM_CHILD,
      freeKwsWebhookPayload.orgId,
      payload,
    );
  }

  async sendSettingsEffectiveValuesChanged(
    freeKwsWebhookPayload: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO<boolean>>,
    orgEnvId: string,
  ): Promise<number | void> {
    this.logger.info(
      `Sending user-permission-changed webhook for OrgEnv: ${orgEnvId} with userId: ${
        freeKwsWebhookPayload.payload.userId
      } with settings ${JSON.stringify(freeKwsWebhookPayload.payload.settings)}`,
    );

    const webhook = await this.webhooksRepository.findOne({
      relations: {
        app: true,
        orgEnv: true,
      },
      where: {
        name: ClassicWebhookName.USER_PERMISSION_CHANGED,
        app: { productId: freeKwsWebhookPayload.productId },
        orgEnvId,
      },
    });

    // Early return here to avoid unnecessary work
    if (webhook === null) {
      return;
    }

    const userExternalId = await this.getUserExternalId(+freeKwsWebhookPayload.payload.userId, orgEnvId);

    const orgEnv = await this.orgEnvRepo.findOneByOrFail({ id: orgEnvId });
    const credentials = { clientId: orgEnv.clientId, secret: orgEnv.clientSecret };

    const userSettings = await this.settingsService.getUserSettings(
      {
        userId: +freeKwsWebhookPayload.payload.userId,
        productId: freeKwsWebhookPayload.productId,
      },
      credentials,
    );

    const freeKwsPayloadSettings = freeKwsWebhookPayload.payload.settings.filter(
      (setting) => `${setting.namespace}.${setting.settingName}` !== DEFAULT_T_AND_C_PERMISSION_NAME,
    );
    const changedSettings = freeKwsPayloadSettings.map((setting) => {
      return (
        userSettings.find(
          (userSetting) =>
            userSetting.namespace === setting.namespace && userSetting.settingName === setting.settingName,
        ) ?? setting
      );
    });

    const payload: UserPermissionChangedPayloadDTO = {
      userId: Number(freeKwsWebhookPayload.payload.userId),
      userExternalId: userExternalId ?? undefined,
      permissions: this.mapPermissions(changedSettings),
    };

    if (Object.keys(payload.permissions).length === 0) {
      this.metrics.increment('sa.classicwrapper.skipped_webhook', 1, [
        `webhook-name:${webhook.name}`,
        `orgid:${webhook.orgEnv.orgId}`,
        `orgenvid:${webhook.orgEnv.id}`,
      ]);
      return;
    }

    return await this.sendAppLevelClassicWebhook(
      ClassicWebhookName.USER_PERMISSION_CHANGED,
      freeKwsWebhookPayload.productId,
      payload,
      orgEnvId,
    );
  }

  async sendGuardianRequestExpired(
    freeKwsWebhookPayload: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto>,
  ) {
    const payload: UnresponsiveParentAccountDeletedPayloadDto = {
      userIds: [Number(freeKwsWebhookPayload.payload.userId)],
    };

    return await this.sendOrgLevelClassicWebhook(
      ClassicWebhookName.UNRESPONSIVE_PARENT_ACCOUNT_DELETED,
      freeKwsWebhookPayload.orgId,
      payload,
    );
  }

  private async getUserExternalId(userId: number, orgEnvId: string): Promise<number | undefined> {
    const user = await this.usersRepository.findOne({
      where: { id: userId, orgEnvId },
    });

    return user?.externalId === null || user?.externalId === undefined ? user?.externalId : Number(user?.externalId);
  }

  private mapPermissions(settings: (SettingsEffectiveValueChangedDTO<boolean> | UserSettingValueDTO)[]) {
    const permissions = new Map<string, boolean | null>();

    for (const setting of settings) {
      const permissionName = SettingsService.settingToPermissionName(setting);
      let permissionValue: boolean | null = Boolean(setting.effectiveValue);
      if ('definition' in setting) {
        permissionValue = SettingsService.classicValue(setting);
      }
      if (permissionValue !== null) {
        permissions.set(permissionName, permissionValue);
      }
    }

    return Object.fromEntries(permissions);
  }

  async sendFamilyGroupDeleted(freeKwsWebhookPayload: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto>) {
    const parent = freeKwsWebhookPayload.payload.members.find((user) => user.role === EMemberRole.Manager);

    if (parent === undefined) {
      return;
    }

    return await Promise.all(
      freeKwsWebhookPayload.payload.members
        .filter((user) => user.role === EMemberRole.Supervised)
        .map(async (user) => {
          const payload: ParentAccountDeletedPayloadDto = {
            // Parent id is intentionally set to userId to match Classic behaviour and as it is irrelevant for the customer
            parentId: Number(user.userId),
          };

          return await this.sendOrgLevelClassicWebhook(
            ClassicWebhookName.PARENT_ACCOUNT_DELETED,
            freeKwsWebhookPayload.orgId,
            payload,
          );
        }),
    );
  }

  private async sendOrgLevelClassicWebhook<T>(classicWebhookName: ClassicWebhookName, orgId: string, payload: T) {
    const webhook = await this.webhooksRepository.findOne({
      relations: {
        orgEnv: true,
      },
      where: { name: classicWebhookName, orgEnv: { orgId: orgId } },
    });

    return await this.sendClassicWebhook<T>(classicWebhookName, webhook, payload);
  }

  private async sendAppLevelClassicWebhook<T>(
    classicWebhookName: ClassicWebhookName,
    productId: string,
    payload: T,
    orgEnvId: string,
  ) {
    const webhook = await this.webhooksRepository.findOne({
      relations: {
        app: true,
        orgEnv: true,
      },
      where: { name: classicWebhookName, app: { productId }, orgEnvId },
    });

    return await this.sendClassicWebhook<T>(classicWebhookName, webhook, payload);
  }

  @Cacheable(300, 'getWebhook', paramsExtractor(0, 1, 2))
  private async getWebhook(webhookName: string, appId: number, orgEnvId: string) {
    return await this.webhooksRepository.findOne({
      relations: {
        app: true,
        orgEnv: true,
      },
      where: { name: webhookName, appId, orgEnvId },
    });
  }

  private async sendAppLevelClassicWebhookWithAppId<T>(
    classicWebhookName: ClassicWebhookName,
    appId: number,
    payload: T,
    orgEnvId: string,
  ) {
    const webhook = await this.getWebhook(classicWebhookName, appId, orgEnvId);

    return await this.sendClassicWebhook<T>(classicWebhookName, webhook, payload);
  }

  private async sendClassicWebhook<T>(
    webhookName: ClassicWebhookName,
    webhook: Webhook | null,
    payload: T,
  ): Promise<number | void> {
    if (webhook === null || !this.config.getAppWebhooksConfig().sendWebhooks) {
      return;
    }

    const classicWebhookDTO: ClassicWebhookDTO<T> = {
      isClassicWebhook: true,
      name: webhookName,
      orgEnvId: webhook.orgEnv.id,
      orgId: webhook.orgEnv.orgId,
      payload: payload,
      productId: webhook.app?.productId,
      secretKey: webhook.secretKey,
      time: new Date().toISOString(),
      url: webhook.url,
    };

    const metricTags: string[] = [
      `webhook-name:${classicWebhookDTO.name}`,
      `orgid:${classicWebhookDTO.orgId}`,
      `orgenvid:${webhook.orgEnv.id}`,
    ];
    this.metrics.increment('sa.classicwrapper.trigger_webhook', 1, metricTags);

    return await this.kafkaProducer.sendMessage(this.TOPIC, classicWebhookDTO);
  }
}

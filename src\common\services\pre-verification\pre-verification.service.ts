import { Inject, Injectable } from '@nestjs/common';
import { AuthLibraryUtils, EncryptionService, KeycloakService } from '@superawesome/freekws-auth-library';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import {
  PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY,
  preverificationBackendPlugin,
  VerificationDTO,
} from '@superawesome/freekws-preverification-service-common';

import { GetParentVerificationsError, PreverificationErrorMessages, PreverificationErrorResponse } from './types';
import { isAxiosError } from '../../../utils';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

@Injectable()
export class PreVerificationService {
  private serviceAccessToken: string;
  private waitForServiceAccessTokenPromise?: Promise<void>;

  constructor(
    @Inject(KEYCLOAK_PROVIDER) private readonly keycloakService: KeycloakService,
    @Inject(PREVERIFICATION_SERVICE_CLIENT_INJECT_KEY)
    private readonly client: NestJsClient<typeof preverificationBackendPlugin>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async getVerificationsByEmail({ email, orgId }: { email: string; orgId: string }): Promise<VerificationDTO[]> {
    if (!AuthLibraryUtils.isTokenValid(this.serviceAccessToken)) {
      await this.setUpServiceToken();
    }

    const encryptedEmail = this.encryptionService.encrypt(email);

    try {
      const {
        data: {
          response: { verifications },
        },
      } = await this.client.getModule('verification').getVerificationStatus(
        {
          params: {
            encryptedEmail: encodeURIComponent(encryptedEmail),
          },
          query: {
            orgId,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${this.serviceAccessToken}`,
          },
        },
      );
      return verifications;
    } catch (error) {
      if (!isAxiosError<PreverificationErrorResponse>(error)) {
        throw error;
      }
      if (error.response?.data?.error.message === PreverificationErrorMessages.RESOURCE_NOT_FOUND) {
        return [];
      }
      throw new GetParentVerificationsError(error);
    }
  }

  private async updateServiceAccessToken(): Promise<void> {
    this.serviceAccessToken = await this.keycloakService.getServiceAccessToken();
  }

  private async setUpServiceToken(): Promise<void> {
    // avoid creating calls in parallel to set up service token
    if (this.waitForServiceAccessTokenPromise) {
      return this.waitForServiceAccessTokenPromise;
    }
    try {
      if (!this.serviceAccessToken || !AuthLibraryUtils.isTokenValid(this.serviceAccessToken)) {
        this.waitForServiceAccessTokenPromise = this.updateServiceAccessToken();
        return await this.waitForServiceAccessTokenPromise;
      }
    } finally {
      this.waitForServiceAccessTokenPromise = undefined;
    }
  }
}

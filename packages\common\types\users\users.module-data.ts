﻿import { IClientsPluginModule, IClientsPluginRequest, THttpMethod } from '@superawesome/freekws-clients-base';

import { UserSearchListResponseDTO, UserSearchQuery } from './users.dto';

class SearchUsersByUsernameRequest implements IClientsPluginRequest<SearchUsersByUsernameRequest> {
  readonly path = '/internal-admin/users';
  readonly httpMethod: THttpMethod = 'GET';
  query: UserSearchQuery;
  returnType: UserSearchListResponseDTO;
}

export class UsersModuleData implements IClientsPluginModule {
  readonly requests = {
    searchUsersByUsername: new SearchUsersByUsernameRequest(),
  };
}

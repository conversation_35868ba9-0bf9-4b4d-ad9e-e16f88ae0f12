import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  ForbiddenException,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { FastifyReply as Response } from 'fastify';
import { Span } from 'nestjs-ddtrace';

import { BadgerService, EbadgerDecision } from './badger.service';
import { TalonService } from './talon.service';
import {
  UserAppActivationDTO,
  UserForgotPasswordDTO,
  UsernameCheckResponseDTO,
  UserRegisterDTO,
  UserResetPasswordDTO,
} from './user.dto';
import { UserService, UserWithEmailDto } from './user.service';
import { AppService } from '../app/app.service';
import { ExtractOrgEnv } from '../common/guards/inject-org-env/inject-org-env.decorator';
import { InjectOrgEnvGuard } from '../common/guards/inject-org-env/inject-org-env.guard';
import { JWT } from '../common/guards/oauth/jwt.decorator';
import { OauthGuard } from '../common/guards/oauth/oauth.guard';
import { And, HasScope, Policies } from '../common/guards/policies';
import { ConfigService } from '../common/services/config/config.service';
import { EmailEnqueueService } from '../common/services/email/email-enqueue.service';
import {
  IEnqueueChildChangedPasswordEmailParams,
  IEnqueueForgottenPasswordEmailParams,
} from '../common/services/email/types';
import { TranslationsService } from '../common/services/translations/translations.service';
import { EAPITags } from '../common/types';
import { isUserToken } from '../oauth/type-check';
import { TJWT } from '../oauth/types';
import { OrgEnv } from '../org-env/org-env.entity';
@Controller()
@Span()
export class UserController {
  logger = new SALogger();
  constructor(
    private readonly userService: UserService,
    private readonly appService: AppService,
    private readonly badgerService: BadgerService,
    private readonly talonService: TalonService,
    private readonly configService: ConfigService,
    private readonly emailEnqueueService: EmailEnqueueService,
    private readonly translationsService: TranslationsService,
    private readonly metricsService: MetricsService,
  ) {}

  @ApiOperation({
    summary: 'Activates a user for a given app',
  })
  @ApiTags(EAPITags.Users)
  @ApiBody({
    type: UserAppActivationDTO,
  })
  @Post('/v1/users/:userId/apps')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(OauthGuard, InjectOrgEnvGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.USER)],
  })
  @Span()
  async activateUser(
    @Body() body: UserAppActivationDTO,
    @Param('userId') userId: number,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @JWT() jwt: TJWT,
    @Res() res: Response,
  ) {
    if (!isUserToken(jwt)) {
      throw new ForbiddenException('Token must be of scope "user"');
    }

    if (jwt.userId !== userId) {
      throw new ForbiddenException('Token does not belong to requested user');
    }

    const { app } = await this.appService.getAppInfoBy(jwt.appId, orgEnv.id);
    if (jwt.clientId !== app.oauthClientId) {
      throw new ForbiddenException('Token does not belong to requested app');
    }

    const appId = app.id;
    const existingActivation = await this.appService.userHasActivation(orgEnv.id, userId, appId);
    if (existingActivation) {
      throw new ConflictException('App activation already exists');
    }

    await this.userService.activateUserToApp(userId, appId, body, orgEnv.id);

    // To align with classic headers and response
    res.header('Content-Type', 'application/json; charset=utf-8');
    res.send('');
    return res;
  }

  @ApiOperation({
    summary: 'Checks if a username is available and appropriate',
  })
  @ApiTags(EAPITags.Users)
  @ApiQuery({
    name: 'username',
    type: String,
    required: true,
    description: 'Username to check for availability and moderation',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Username availability status',
    type: UsernameCheckResponseDTO,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Missing or empty username parameter',
  })
  @Get('/v1/users/check-username')
  @HttpCode(HttpStatus.OK)
  @Public()
  @UseGuards(InjectOrgEnvGuard)
  async checkUsername(
    @Query('username') username: string,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Headers('accept-language') language?: string,
  ) {
    if (!username || username.trim() === '') {
      throw new BadRequestException('Username query parameter is required.');
    }
    const usernameAvailable = await this.userService.isUsernameAvailable(username, orgEnv.id);
    const badgerResult = await this.badgerService.moderateUsername(username, language);
    const passesModeration = badgerResult !== EbadgerDecision.REJECT;

    const reasons = [
      !usernameAvailable && 'Username is already taken.',
      !passesModeration && `Username moderation judgement is '${badgerResult}'.`,
    ].filter(Boolean) as string[]; // filters out falsy values like `false`

    return {
      username,
      available: usernameAvailable && passesModeration,
      details: {
        isValid: passesModeration,
        isAvailable: usernameAvailable,
        reasons: reasons.length > 0 ? reasons : undefined,
      },
    };
  }

  @ApiOperation({
    summary: 'Registers a user',
    description: 'Create a new user account.',
  })
  @ApiTags(EAPITags.Users)
  @Post('/v2/users')
  @Public()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(InjectOrgEnvGuard)
  @Span()
  async registerUser(
    @Body() body: UserRegisterDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Headers() headers: Record<string, string>,
    @Param('country') country?: string,
    @Headers('CloudFront-Viewer-Country') countryHeader?: string,
  ) {
    const verified = await this.talonService.verify(body.token, headers, body.parentEmail);
    if (!verified) {
      throw new ForbiddenException('Suspected Bot from Talon');
    }

    const signupCountry = body.country ?? country ?? countryHeader ?? 'ZZ';

    if (body.username) {
      const usernameAvailable = await this.userService.isUsernameAvailable(body.username, orgEnv.id);
      if (!usernameAvailable) {
        throw new ConflictException('Username is already taken');
      }
      const badgerResult = await this.badgerService.moderateUsername(body.username, body.language);
      const passesModeration = badgerResult !== EbadgerDecision.REJECT;
      if (!passesModeration) {
        throw new BadRequestException('Username rejected by moderation');
      }
    }

    const userId = await this.userService.createUserV2(body, signupCountry, orgEnv.id);
    return {
      id: userId,
    };
  }

  @ApiOperation({
    summary: 'Triggers a forgot password email',
  })
  @ApiTags(EAPITags.Apps)
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'app not found',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'success',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @Post('/v1/users/forgot-password')
  @UseGuards(InjectOrgEnvGuard)
  async forgotPassword(@Body() body: UserForgotPasswordDTO, @ExtractOrgEnv() orgEnv: OrgEnv) {
    const app = await this.appService.appByOauthClientId(orgEnv.id, body.appName);
    if (body.appName && app === null) {
      throw new NotFoundException(`app ${body.appName} not found`);
    }

    const nianticConfig = this.configService.getNianticConfig();
    const tokenExpiry = nianticConfig.resetPwdTokenExpiryTimeInHours;
    this.userService
      .createPasswordResetToken(body.username, orgEnv.id, tokenExpiry)
      ?.then(async (userWithEmail: UserWithEmailDto) => {
        if (!(userWithEmail && userWithEmail.user && userWithEmail.email)) {
          return;
        }
        const language = userWithEmail.user.language;
        const passwordResetUrl = `${nianticConfig.frontendAuthUrl}/auth`;
        const passwordResetParams = {
          password_reset_token: userWithEmail.user.passwordResetToken ?? '',
          user: encodeURIComponent(userWithEmail.user.username as string),
          language: userWithEmail.user.language.toString(),
          app: body?.appName ?? nianticConfig.appClientId,
        };

        const brandingDto = await this.translationsService.getTranslationWithBestMatch(orgEnv.id, language);
        if (brandingDto === undefined) {
          throw new NotFoundException(language, 'language not found');
        }

        const link = this.generatePasswordResetUrl(passwordResetUrl, passwordResetParams);
        const forgottenPasswordEmailParams: IEnqueueForgottenPasswordEmailParams = {
          kwsFaqLink: brandingDto.parentSupportUrl ?? '',
          customerPrivacyPolicyLink: brandingDto.privacyPolicyUrl,
          customPrimaryColour: brandingDto?.primaryColor,
          resetPasswordLink: link,
          language: language,
          toEmailAddress: userWithEmail.email,
        };

        await this.emailEnqueueService.enqueueForgottenPasswordEmail(
          orgEnv.orgId,
          passwordResetParams.app,
          orgEnv.id,
          forgottenPasswordEmailParams,
          brandingDto,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          app!.mode,
        );
      })
      .catch((error) => {
        this.logger.error('Failed to send forgotten password email', { err: error });
        this.metricsService.distribution('sa.classicwrapper.forgotten_password.error', 1, 1, [
          `appName:${body.appName}`,
        ]);
      });
  }

  generatePasswordResetUrl(initialUrl: string, params: { [key: string]: string }): string {
    const paramNames = Object.keys(params);

    const paramsInVariables = paramNames.filter((param) => initialUrl.includes(`{${param}}`));
    const replacedUrl = paramsInVariables.reduce((acc, param) => acc.replace(`{${param}}`, params[param]), initialUrl);

    const additionalVariables = paramNames.filter((param) => !initialUrl.includes(`{${param}}`));
    const parsedUrl = new URL(replacedUrl);
    for (const param of additionalVariables) {
      parsedUrl.searchParams.append(param, params[param]);
    }

    return parsedUrl.href;
  }

  @ApiOperation({
    summary:
      "This endpoint allows you to use a token to reset a user's password. The reset password token is\n * generated by KWS and sent to the user (or their parent) via email when the forgot password flow is triggered using\n * the 'forgot password' endpoint. After the password is reset, a confirmation email will be sent to the user (or\n * their parent, depending on which email address is set in the account)",
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'success',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'bad request',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @Post('/v1/users/reset-password')
  @UseGuards(InjectOrgEnvGuard)
  async resetPassword(@Body() body: UserResetPasswordDTO, @ExtractOrgEnv() orgEnv: OrgEnv) {
    const userWithEmail = await this.userService.resetPassword(body.token, body.newPassword, orgEnv.id);
    if (!(userWithEmail && userWithEmail.user && userWithEmail.email)) {
      throw new BadRequestException();
    }
    const app = await this.appService.appByOauthClientId(orgEnv.id, body.appName);

    if (app === null) {
      throw new BadRequestException();
    }

    const language = userWithEmail.user.language;
    const brandingDto = await this.translationsService.getTranslationWithBestMatch(orgEnv.id, language);
    if (brandingDto === undefined) {
      throw new NotFoundException(language, 'language not found');
    }
    const nianticConfig = this.configService.getNianticConfig();
    const emailParams: IEnqueueChildChangedPasswordEmailParams = {
      kwsFaqLink: brandingDto.parentSupportUrl ?? '',
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      childUsername: userWithEmail.user.username!,
      language: language,
      supportEmailAddress: nianticConfig.supportEmailAddress,
      toEmailAddress: userWithEmail.email,
    };

    await this.emailEnqueueService.enqueueChildChangedPasswordEmail(
      orgEnv.orgId,
      body.appName,
      orgEnv.id,
      emailParams,
      brandingDto,
      app.mode,
    );
  }
}

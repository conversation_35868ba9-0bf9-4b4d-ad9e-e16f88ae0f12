#!/bin/bash

# Test script to verify security headers are properly implemented
# Run this script while your server is running locally

echo "🔒 Testing Security Headers Implementation"
echo "========================================"
echo ""

# Default port - adjust if your server runs on a different port
PORT=${1:-80}
URL="http://localhost:$PORT"

# Check if server is running
echo "Testing server at: $URL"
echo ""

# Function to test a specific endpoint
test_endpoint() {
    local endpoint=$1
    echo "Testing endpoint: $endpoint"
    echo "----------------------------------------"
    
    # Use curl to fetch headers only (-I flag)
    response=$(curl -s -I "$URL$endpoint" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo "❌ Error: Could not connect to server at $URL"
        echo "   Make sure your server is running on port $PORT"
        echo ""
        return 1
    fi
    
    # Check for security headers
    check_header "X-Powered-By" "REMOVED" "$response"
    check_header "Strict-Transport-Security" "max-age=31536000; includeSubDomains; preload" "$response"
    check_header "X-Frame-Options" "SAMEORIGIN" "$response"
    check_header "Content-Security-Policy" "default-src 'self'" "$response"
    check_header "Referrer-Policy" "strict-origin-when-cross-origin" "$response"
    check_header "Cache-Control" "no-cache, no-store, must-revalidate, private" "$response"
    check_header "X-Content-Type-Options" "nosniff" "$response"
    check_header "X-DNS-Prefetch-Control" "off" "$response"
    
    echo ""
}

# Function to check if a specific header exists
check_header() {
    local header_name=$1
    local expected_value=$2
    local response=$3
    
    if [ "$header_name" = "X-Powered-By" ]; then
        # Special case: X-Powered-By should be removed
        if echo "$response" | grep -qi "X-Powered-By:"; then
            echo "❌ $header_name: Still present (should be removed)"
        else
            echo "✅ $header_name: Successfully removed"
        fi
    else
        # Check if header exists and has expected value
        header_line=$(echo "$response" | grep -i "$header_name:")
        if [ -n "$header_line" ]; then
            if echo "$header_line" | grep -q "$expected_value"; then
                echo "✅ $header_name: $expected_value"
            else
                echo "⚠️  $header_name: Present but different value"
                echo "   Found: $(echo "$header_line" | sed 's/^[^:]*: //')"
            fi
        else
            echo "❌ $header_name: Missing"
        fi
    fi
}

# Test common endpoints
echo "Testing common endpoints..."
echo ""

# Test healthcheck endpoint (if available)
test_endpoint "/healthcheck"

# Test root endpoint
test_endpoint "/"

echo "🔒 Security Headers Test Complete!"
echo ""
echo "Legend:"
echo "✅ = Header correctly implemented"
echo "❌ = Header missing or incorrect"
echo "⚠️  = Header present but with different value"
echo ""
echo "How to use this script:"
echo "1. Start your server locally"
echo "2. Run: bash test-security-headers.sh [PORT]"
echo "   (PORT defaults to 80 if not specified)"
echo ""
echo "Example: bash test-security-headers.sh 3000"
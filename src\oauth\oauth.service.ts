import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EncryptionService } from '@superawesome/freekws-auth-library';
import {
  EOAuthScope,
  OAuthAuthorisePayloadDTO,
  OAuthTokenDTO,
  ParentState,
} from '@superawesome/freekws-classic-wrapper-common';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { createHash, randomBytes } from 'crypto';
import { formatDate } from 'date-fns';
import { Span } from 'nestjs-ddtrace';
import { Repository } from 'typeorm';

import { JWKService } from './jwk.service';
import { RefreshToken } from './refresh-token.entity';
import { JwtUserPayload, TJWT, TJwtPayload } from './types';
import { App } from '../app/app.entity';
import { AppService } from '../app/app.service';
import { IAppOauthClient } from '../app/types';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { OrgEnv } from '../org-env/org-env.entity';
import { OrgEnvService } from '../org-env/org-env.service';
import { IClientCredentials } from '../org-env/types';
import { Activation } from '../user/activation.entity';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

export const secondsInADay = 60 * 60 * 24;

@Injectable()
@Span()
export class OauthService {
  constructor(
    private readonly jwkService: JWKService,
    @InjectRepository(RefreshToken) private readonly refreshTokenRepo: Repository<RefreshToken>,
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(App) private readonly appRepo: Repository<App>,
    @InjectRepository(OrgEnv) private readonly orgEnvRepo: Repository<OrgEnv>,
    @InjectRepository(Activation) private readonly activationRepo: Repository<Activation>,
    private readonly userService: UserService,
    private readonly settingsService: SettingsService,
    private readonly ageGateService: AgeGateService,
    private readonly encryptionService: EncryptionService,
    private readonly metricsService: MetricsService,
    private readonly appService: AppService,
    private readonly orgEnvService: OrgEnvService,
  ) {}

  static extractTokenData(data: OAuthTokenDTO, client: IAppOauthClient): TJwtPayload {
    if (data.scope === EOAuthScope.APP || data.scope === EOAuthScope.MOBILE_APP) {
      return {
        clientId: client.clientId,
        appId: client.appId,
        scope: data.scope,
      };
    }

    throw new BadRequestException('Invalid `scope` value');
  }

  private trackClientType(clientType: string, orgEnvId: string) {
    this.metricsService.increment('sa.classicwrapper.oauth.client', 1, [
      `client_type:${clientType}`,
      `org_env_id:${orgEnvId}`,
    ]);
  }

  async getAccessToken(data: OAuthTokenDTO, client: IAppOauthClient): Promise<string> {
    const payload = OauthService.extractTokenData(data, client);

    this.trackClientType(data.scope || 'unknown', client.orgEnvId);

    return await this.jwkService.sign(payload, client.orgEnvId);
  }

  async getRefreshToken(scope: EOAuthScope | undefined, client: IAppOauthClient, username?: string) {
    let userId: number | undefined;
    if (username) {
      const user = await this.userService.getByUsername(client.orgEnvId, username);
      if (!user) {
        throw new NotFoundException(`Username ${username} not found`);
      }
      userId = user.id;
    }
    return await this.createRefreshToken({
      appId: client.appId,
      orgEnvId: client.orgEnvId,
      clientId: client.clientId,
      scope: scope ?? '',
      userId,
    });
  }

  async getRefreshTokenWithUserId(userId: number, client: IAppOauthClient) {
    return await this.createRefreshToken({
      appId: client.appId,
      orgEnvId: client.orgEnvId,
      clientId: client.clientId,
      scope: EOAuthScope.USER,
      userId,
    });
  }

  async createRefreshToken(refreshTokenToCreate: Omit<RefreshToken, 'id' | 'token' | 'expires'>) {
    const token = this.generateToken();
    const expiresAt = Date.now() + 1000 * secondsInADay;
    return await this.refreshTokenRepo.save({
      ...refreshTokenToCreate,
      expires: new Date(expiresAt),
      token,
    });
  }

  private generateToken() {
    return randomBytes(32).toString('hex');
  }

  async getAccessTokenFromRefreshToken(refreshToken: string, data: OAuthTokenDTO, client: IAppOauthClient) {
    const refreshTokenEntity = await this.refreshTokenRepo.findOne({
      where: {
        orgEnvId: client.orgEnvId,
        appId: client.appId,
        token: refreshToken,
      },
    });

    if (!refreshTokenEntity) {
      throw new BadRequestException('Invalid refresh token');
    }

    if (refreshTokenEntity.expires < new Date()) {
      throw new BadRequestException('Refresh token expired');
    }

    if (refreshTokenEntity.scope === EOAuthScope.USER) {
      const { userId } = refreshTokenEntity;
      if (!userId) {
        throw new BadRequestException('Corrupt refresh token');
      }
      return await this.createUserTokenFromUserId(userId, client);
    }

    return await this.getAccessToken(
      {
        ...data,
        scope: refreshTokenEntity.scope as EOAuthScope,
      },
      client,
    );
  }

  async createUserTokenFromUserId(userId: number, client: IAppOauthClient) {
    const { orgEnvId } = client;
    const user = await this.userRepo.findOneByOrFail({
      orgEnvId,
      id: userId,
    });
    return await this.createUserToken(client, orgEnvId, user);
  }

  async createUserTokenFromUsername(client: IAppOauthClient, username: string) {
    const { orgEnvId } = client;
    const user = await this.userService.getByUsername(orgEnvId, username);
    if (!user) {
      throw new NotFoundException(`Username ${username} not found`);
    }
    return await this.createUserToken(client, orgEnvId, user);
  }

  private async getUserSettings(productId: string, userId: number, credentials: IClientCredentials) {
    return await this.settingsService.getUserSettings(
      {
        productId,
        userId,
      },
      credentials,
    );
  }

  private async createUserToken(client: IAppOauthClient, orgEnvId: string, user: User) {
    const { app } = await this.appService.getAppInfoBy(client.appId, orgEnvId);
    const orgEnv = await this.orgEnvService.getById(orgEnvId);
    const credentials: IClientCredentials = {
      clientId: orgEnv.clientId,
      secret: orgEnv.clientSecret,
    };

    const userHasActivation = await this.userService.getUserActivation(orgEnvId, user.id, app.id);
    let userSettings: UserSettingValueDTO[] = [];
    if (userHasActivation) {
      userSettings = await this.getUserSettings(app.productId, user.id, credentials);
    }

    const permissions = Object.keys(SettingsService.transformSettingsToAllPermissions(userSettings));

    const parentEmail = await this.userService.getParentEmail(user.id);
    let parentState: ParentState | undefined;
    if (parentEmail) {
      parentState = await this.userService.getParentState(parentEmail, orgEnv.orgId);
    }

    const ageGateInfo = await this.ageGateService.getConsentAgeForCountry(
      {
        location: user.signUpCountry,
        dob: user.dateOfBirth ? formatDate(user.dateOfBirth, 'yyyy-MM-dd') : undefined,
      },
      credentials,
    );

    const parentVPCVerified = parentState?.idVerified ?? false;
    const payload = {
      userId: user.id,
      appId: client.appId,
      clientId: app.oauthClientId, // Client ID is the oauthClientId, not orgEnvClientId
      scope: EOAuthScope.USER,
      appPermissions: permissions,
      signUpCountry: user.signUpCountry,
      parentEmail,
      parentVerified: parentVPCVerified,
      parentState: {
        idVerified: parentVPCVerified,
        deleted: false,
        expired: false,
        rejected: false,
        verified: parentVPCVerified,
      } as ParentState,
      isMinor: ageGateInfo.underAgeOfDigitalConsent ?? true,
    };

    return await this.jwkService.sign(payload, client.orgEnvId);
  }

  async getAuthCode(data: OAuthAuthorisePayloadDTO, jwt: JwtUserPayload, orgEnv: OrgEnv) {
    const { app } = await this.appService.getAppInfoBy(jwt.appId, orgEnv.id);
    if (!this.isValidCallbackUrl(app, data.redirect_uri)) {
      throw new BadRequestException('Invalid or missing redirect_uri parameter');
    }

    const authCodePayload = {
      scope: EOAuthScope.USER as const,
      userId: jwt.userId,
      clientId: jwt.clientId,
    };

    const codeChallengePayload: {
      codeChallenge?: string;
      codeChallengeMethod?: string;
    } = {};

    if (data.code_challenge) {
      codeChallengePayload.codeChallenge = this.encryptionService.encrypt(data.code_challenge);
      codeChallengePayload.codeChallengeMethod = this.encryptionService.encrypt(data.code_challenge_method || 'plain');
    }

    const fullAuthCodePayload = {
      ...authCodePayload,
      ...codeChallengePayload,
    };

    return await this.jwkService.sign(fullAuthCodePayload, orgEnv.id, 120);
  }

  async getJwt(code: string, orgEnvId: string) {
    return await this.jwkService.verify(code, orgEnvId);
  }

  async verifyCode(
    data: {
      appId: number;
      clientId: string;
      codeJwt: TJWT;
      codeVerifier: string | undefined;
      redirect_uri: string;
    },
    orgEnvId: string,
  ) {
    const { codeJwt } = data;
    if (codeJwt.clientId !== data.clientId) {
      throw new BadRequestException('Client ID mismatch between JWT and request');
    }

    const { app } = await this.appService.getAppInfoBy(data.appId, orgEnvId);
    if (!this.isValidCallbackUrl(app, data.redirect_uri)) {
      throw new BadRequestException('Redirect URI mismatch between app and request');
    }

    const { codeChallenge, codeChallengeMethod } = codeJwt;
    if (codeChallenge && codeChallengeMethod) {
      if (!data.codeVerifier) {
        throw new BadRequestException('Code contains code_challenge but request contains no code_verifier');
      }
      const challenge = this.encryptionService.decrypt(codeChallenge);
      const method = this.encryptionService.decrypt(codeChallengeMethod);
      if (method === 'plain') {
        return data.codeVerifier === challenge;
      }
      if (method === 'S256') {
        const hash = createHash('sha256').update(data.codeVerifier).digest();
        return challenge === hash.toString('base64').replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '');
      }
      throw new BadRequestException('Invalid code_challenge_method within code. Must be one of "plain" or "S256"');
    }

    return true;
  }

  private isValidCallbackUrl(app: App, redirectUri: string) {
    if (app.oauthCallbackUrls) {
      return app.oauthCallbackUrls.some((callbackUrl) => callbackUrl.url === redirectUri);
    }

    return false;
  }
}

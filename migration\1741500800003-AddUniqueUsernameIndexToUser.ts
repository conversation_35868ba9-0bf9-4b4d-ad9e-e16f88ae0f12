import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueUsernameIndexToUser1741500800003 implements MigrationInterface {
    name = 'AddUniqueUsernameIndexToUser1741500800003';
    transaction = false;

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS "user_username_orgEnvId_idx"
                ON "user" (LOWER("username"), "orgEnvId")
                WHERE "deletedAt" IS NULL
                    AND "username" IS NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX CONCURRENTLY IF EXISTS "user_username_orgEnvId_idx"
        `);
    }
}
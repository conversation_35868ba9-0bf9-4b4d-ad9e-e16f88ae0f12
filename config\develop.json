{"logger": {"console": {"level": "LOG_LEVEL"}}, "http": {"port": "80", "cors": {"origin": "https://cors-origin.com"}}, "database": {"type": "postgres", "masterUrl": "postgresql://postgres:dev@localhost:5432/postgres", "slaveUrls": "postgresql://postgres:dev@localhost:5432/postgres"}, "kafka": {"kafkaHost": "brokerhost1.dev:9999,brokerhost2.dev:9999", "outOfOrderTopic": "out-of-order-topic"}, "keycloak": {"realm": "kws", "secret": "c3a5909b-5434-52af-9411-8099a2dac73c", "authServerUrl": "http://keycloak:8080/auth", "timeoutMs": "10000", "expirationTime": "259200", "realmUrl": "http://keycloak:8080/auth"}, "ageGateService": {"baseURL": "http://stub:4747", "requestTimeout": "20000"}, "settingsBackend": {"baseURL": "http://stub:4748", "requestTimeout": "20000"}, "familyService": {"baseURL": "http://stub:4749", "requestTimeout": "20000"}, "preVerificationService": {"baseURL": "http://stub:4750", "requestTimeout": "20000"}, "analyticService": {"baseURL": "http://stub:4750", "authHeader": ""}, "devPortalBackend": {"baseURL": "http://stub:4752", "requestTimeout": "20000"}, "emailService": {"kafkaTopic": "develop-topic", "footer": {"logoSrc": "EMAIL_SA_LOGO_SRC"}, "header": {"headerSrc": "EMAIL_SA_DEFAULT_HEADER_SRC"}}, "brandingApiClient": {"baseURL": "http://stub:4545"}, "analyticsService": {"authString": "SomeAuthString"}, "amplitude": {"apiKey": "amplitude-api-key", "serverUrl": "https://amplitude-server-url.com"}, "niantic": {"frontendAuthUrl": "https://example.com", "appDisplayName": "niantic", "appClientId": "83", "resetPwdTokenExpiryTimeInHours": 1}}
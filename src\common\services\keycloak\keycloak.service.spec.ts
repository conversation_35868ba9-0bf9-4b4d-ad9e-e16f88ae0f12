import { Test, TestingModule } from '@nestjs/testing';
import { TKeycloakConfig } from '@superawesome/freekws-auth-library';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';

import { ClientKeycloakService } from './client-keycloak.service';
import { ConfigService } from '../config/config.service';

jest.mock('@superawesome/freekws-metrics-nestjs-service');

const mockGetToken = jest.fn().mockImplementation(() => {
  return 'ACCESS_TOKEN';
});
jest.mock('@superawesome/freekws-auth-library', () => {
  const originalModule = jest.requireActual('@superawesome/freekws-auth-library');
  return {
    ...originalModule,
    KeycloakService: jest.fn().mockImplementation(() => ({
      ...originalModule.KeycloakService,
      getScopedAccessToken: mockGetToken,
    })),
    AuthLibraryUtils: {
      ...originalModule.AuthLibraryUtils,
      isTokenValid: jest.fn().mockReturnValue(false), // Always return false to force token refresh
    },
  };
});
const config: TKeycloakConfig = {
  authServerUrl: 'https://auth',
  clientId: 'classic-id',
  realm: 'classic-realm',
  secret: 'classic-secret',
  realmUrl: 'http://realm',
  timeoutMs: 20,
};

const mockConfig = {
  getKeycloak: jest.fn().mockReturnValue(config),
};

const mockMetricsService = {
  histogram: jest.fn(),
  distribution: jest.fn(),
  increment: jest.fn(),
};

describe('KeycloakService', () => {
  let service: ClientKeycloakService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClientKeycloakService,
        { provide: ConfigService, useValue: mockConfig },
        { provide: MetricsService, useValue: mockMetricsService },
      ],
    }).compile();

    service = module.get<ClientKeycloakService>(ClientKeycloakService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getClientToken', () => {
    it('should return token', async () => {
      await expect(service.getClientToken({ clientId: '', secret: '' })).resolves.toEqual('ACCESS_TOKEN');
    });

    it('should return renewed token', async () => {
      mockGetToken.mockResolvedValueOnce('NEW_ACCESS_TOKEN');
      await expect(service.getClientToken({ clientId: '', secret: '' })).resolves.toEqual('NEW_ACCESS_TOKEN');
    });
  });
});

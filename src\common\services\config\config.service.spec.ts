import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON>t, AuditSubscriber } from '@superawesome/freekws-nestjs-audit';

import { ConfigService } from './config.service';

describe('ConfigService', () => {
  let service: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConfigService],
    }).compile();

    service = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getTypeormConfig', () => {
    it('should return the typeorm config', () => {
      expect(service.getTypeormConfig()).toMatchObject({
        autoLoadEntities: true,
        cli: {
          migrationsDir: 'migration',
        },
        entities: ['dist/**/*.entity.js', Audit],
        extra: {
          max: 3,
        },
        logging: false,
        migrations: ['dist/migration/*.js'],
        replication: {
          master: {
            url: '***************************************/postgres',
          },
          slaves: [
            {
              url: '***************************************/postgres',
            },
          ],
        },
        subscribers: [AuditSubscriber],
        synchronize: false,
        type: 'postgres',
      });
    });
  });

  describe('getHealthcheckConfig', () => {
    it('should return the healthcheck config', () => {
      expect(service.getHealthcheckConfig()).toEqual({
        intervalMs: 60000,
      });
    });
  });

  describe('getHttpConfig', () => {
    it('should return the http config', () => {
      expect(service.getHttpConfig()).toEqual({
        cors: {
          origin: 'https://cors-origin.com',
        },
        port: 80,
      });
    });
  });

  describe('getKeycloak', () => {
    it('should return the keycloak config', () => {
      expect(service.getKeycloak()).toEqual({
        additionalTrustedIssuers: [],
        audience: 'classic-wrapper',
        authServerUrl: 'http://keycloak:8080/auth',
        clientId: 'classic-wrapper-api',
        expirationTime: 259200,
        realm: 'kws',
        realmUrl: 'http://auth.kws.staging.superawesome.com/auth',
        secret: 'c3a5909b-5434-52af-9411-8099a2dac73c',
        timeoutMs: 10000,
      });
    });
  });

  describe('getAgeGateCircuitBreakerConfig', () => {
    it('should return the http config', () => {
      expect(service.getAgeGateCircuitBreakerConfig()).toEqual({
        errorThresholdPercentage: expect.any(Number),
        resetTimeoutMs: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getAgeGateService', () => {
    it('should return the http config', () => {
      expect(service.getAgeGateService()).toEqual({
        bailOnStatus: [400, 401, 403, 404, 429],
        baseURL: 'http://host.docker.internal:1182',
        initialRetryDelay: 0,
        retries: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getCacheable', () => {
    it('should return the http config', () => {
      expect(service.getCacheable()).toEqual({
        defaultTtlSecs: 60,
        enabled: true,
        maxCacheSizeEntries: 1000,
        metricsNamespace: 'classicWrapperBackend',
      });
    });
  });

  describe('getKafka', () => {
    it('should return the kafka config', () => {
      const result = service.getKafka();
      expect(result.kafkaHost).toBeDefined();
      expect(result.outOfOrderTopic).toEqual('out-of-order-topic');
    });
  });

  describe('getAnalytic', () => {
    it('should return the analytics service config', () => {
      expect(service.getAnalytic()).toEqual({
        authorizationHeader: '',
        baseURL: 'http://host.docker.internal:1186',
        upstream: 'freekws-analytic-service',
      });
    });
  });

  describe('getSettingsService', () => {
    it('should return the settings backend config', () => {
      expect(service.getSettingsService()).toEqual({
        bailOnStatus: [400, 401, 403, 404, 429],
        baseURL: 'http://host.docker.internal:1183',
        initialRetryDelay: 0,
        retries: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getSettingsServiceCircuitBreakerConfig', () => {
    it('should return the settings backend CB config', () => {
      expect(service.getSettingsServiceCircuitBreakerConfig()).toEqual({
        errorThresholdPercentage: expect.any(Number),
        resetTimeoutMs: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getFamilyService', () => {
    it('should return the family service config', () => {
      expect(service.getFamilyService()).toEqual({
        bailOnStatus: [400, 401, 403, 404, 429],
        baseURL: 'http://host.docker.internal:1184',
        initialRetryDelay: 0,
        retries: 0,
        timeoutMs: 3000,
      });
    });
  });

  describe('getFamilyServiceCircuitBreakerConfig', () => {
    it('should return the family service CB config', () => {
      expect(service.getFamilyServiceCircuitBreakerConfig()).toEqual({
        errorThresholdPercentage: expect.any(Number),
        resetTimeoutMs: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getPreVerificationService', () => {
    it('should return the preverifications service config', () => {
      expect(service.getPreVerificationService()).toEqual({
        bailOnStatus: [400, 401, 403, 404, 429],
        baseURL: 'http://host.docker.internal:1185',
        initialRetryDelay: 0,
        retries: 0,
        timeoutMs: 3000,
      });
    });
  });

  describe('getPreVerificationServiceCircuitBreakerConfig', () => {
    it('should return the preverifications service CB config', () => {
      expect(service.getPreVerificationServiceCircuitBreakerConfig()).toEqual({
        errorThresholdPercentage: expect.any(Number),
        resetTimeoutMs: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getEncryptionConfig', () => {
    it('should return the encryption config', () => {
      expect(service.getEncryptionConfig()).toEqual({
        secrets: ['0:80f90f75f38a86d93ff18b8a1f46409e423c6428ac74ee44b3c57c773e6a38e9'],
        secretVersion: 0,
      });
    });
  });

  describe('getDevPortalService', () => {
    it('should return the settings backend config', () => {
      expect(service.getDevPortalService()).toEqual({
        bailOnStatus: [400, 401, 403, 404, 429],
        baseURL: 'http://host.docker.internal:1187',
        initialRetryDelay: 0,
        retries: 0,
        timeoutMs: 3000,
      });
    });
  });

  describe('getDevPortalServiceCircuitBreakerConfig', () => {
    it('should return the settings backend CB config', () => {
      expect(service.getFamilyServiceCircuitBreakerConfig()).toEqual({
        errorThresholdPercentage: expect.any(Number),
        resetTimeoutMs: expect.any(Number),
        timeoutMs: expect.any(Number),
      });
    });
  });

  describe('getEmailService', () => {
    it('should return the email config', () => {
      expect(service.getEmailService()).toEqual({
        footer: {
          logoSrc: 'test.jpg',
        },
        header: {
          headerSrc: 'test.jpg',
        },
        kafkaTopic: 'test-email-topic',
      });
    });
  });

  describe('getLinks', () => {
    it('should return the links config', () => {
      expect(service.getLinks()).toEqual({
        kwsFaqLink: 'https://parents.kidswebservices.com/parent-verification-faqs/',
        kwsPrivacyPolicyLink: 'https://www.kidswebservices.com/privacy-policy/',
        kwsTermsLink: 'https://www.kidswebservices.com/terms-of-use/',
        kwsHelpCentreLink: 'https://parents.kidswebservices.com/',
      });
    });
  });
});

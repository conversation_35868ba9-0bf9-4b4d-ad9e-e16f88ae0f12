import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserUsernameOrgEnvIdIdx1741500900004
    implements MigrationInterface {
    name = 'AddUserUsernameOrgEnvIdIdx1741500900004';
    transaction = false;

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_username_orgEnvId_idx"
                ON "user" (LOWER("username"), "orgEnvId")
                WHERE "deletedAt" IS NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX CONCURRENTLY IF EXISTS "user_username_orgEnvId_idx"
        `);
    }
}

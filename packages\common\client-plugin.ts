﻿import { ClientsPlugin } from '@superawesome/freekws-clients-base';

import { UsersModuleData } from './types/users/users.module-data';

export const CLASSIC_WRAPPER_BACKEND_API_CLIENT_INJECT_KEY = 'classicWrapperBackend';
export const CLASSIC_WRAPPER_BACKEND_API_CLIENT_METRICS = 'CLASSIC_WRAPPER_BACKEND_API_CLIENT_METRICS';
export const CLASSIC_WRAPPER_BACKEND_API_CLIENT_UPSTREAM = 'freekws-classic-wrapper-backend-client';
export const classicWrapperBackend = new ClientsPlugin(
  {
    injectKey: CLASSIC_WRAPPER_BACKEND_API_CLIENT_INJECT_KEY,
    metricsKey: CLASSIC_WRAPPER_BACKEND_API_CLIENT_METRICS,
    upstream: CLASSIC_WRAPPER_BACKEND_API_CLIENT_UPSTREAM,
  },
  { users: new UsersModuleData() },
  'INTERNAL',
);

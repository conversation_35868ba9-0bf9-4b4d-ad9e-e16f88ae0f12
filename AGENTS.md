# Agent Guidelines for FreeKWS Classic Wrapper Backend

This repository contains the FreeKWS Classic Wrapper API - a NestJS backend that provides backwards compatibility with Classic KWS while using CM2 services.

## Implementation Principles

- **Progressive Development**: Implement solutions in logical stages, pause after each component to check requirements
- **Scope Management**: Only implement what is explicitly requested; ask permission before modifying unmentioned components
- **Minimal Viable Interpretation**: When requirements are ambiguous, choose the simplest interpretation

## Testing Guidelines

### Unit Tests
- Keep tests simple and not overly verbose - avoid going crazy with edge cases
- Reuse data within tests instead of recreating the same data
- Use effective variable names and helper methods for clarity

### End-to-End Tests
- Do NOT use Jest for e2e tests
- Use nock to mock endpoints - check all calling methods to understand endpoints before implementing
- Use `makeRequest` function from `test/utils/request-helper.ts` for better debug logs
- Run e2e tests with: `./scripts/docker-npm-run.sh test:e2e --no-clean`
- Follow patterns from existing e2e tests when stuck

## Project Structure
- Follows NestJS domain-based convention with folders per resource/entity
- Common folder contains middlewares, interceptors, adapters, guards
- Shared services in `common/services` subfolder with individual modules

import { TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EMemberRole } from '@superawesome/freekws-family-service-common';
import {
  FamiliesGuardianRequestExpiredPayloadDto,
  FamiliesGroupDeletedPayloadDto,
  FamiliesUserAddedToFamilyDTO,
  SettingsEffectiveValuesChangedPayloadDTO,
} from '@superawesome/freekws-queue-messages/webhook';
import { ClassicWebhookName } from '@superawesome/freekws-queue-messages/webhook/classic-webhook.dto';
import { ChildActivationDeletedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-activation-deleted.payload.dto';
import { ChildLinkedToParentPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/child-linked-to-parent.payload.dto';
import { ParentAccountDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/parent-account-deleted.payload.dto';
import { UnresponsiveParentAccountDeletedPayloadDto } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/unresponsive-parent-account-deleted.payload.dto';
import { UserPermissionChangedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/classic/user-permission-changed.payload.dto';
import { FamiliesUserRemovedFromFamilyDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/families-user-removed-from-family.payload.dto';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { Repository } from 'typeorm';

import { AppLevelWebhookPayload, OrgLevelWebhookPayload } from './types';
import { Webhook } from './webhook.entity';
import { WebhookService } from './webhook.service';
import { KAFKA_PRODUCER } from '../common/services/kafka/kafka-producer.module';
import { SettingsService } from '../common/services/settings/settings.service';
import { Testing } from '../common/utils';
import { OrgEnv } from '../org-env/org-env.entity';
import { User } from '../user/user.entity';

const mockKafka = {
  sendMessage: jest.fn(),
};

describe('WebhookService', () => {
  let service: WebhookService;
  let webhooksRepository: Repository<Webhook>;
  let usersRepository: Repository<User>;
  let orgEnvRepo: Repository<OrgEnv>;

  const orgEnvId = '123';

  const mockedWebhook = {
    orgEnv: { id: orgEnvId, orgId: '1234' },
    app: { productId: '123' },
    secretKey: '123',
    url: '123123',
  } as Webhook;

  beforeEach(async () => {
    const module: TestingModule = await Testing.createModule({
      providers: [WebhookService, { provide: KAFKA_PRODUCER, useValue: mockKafka }],
    });

    service = module.get<WebhookService>(WebhookService) as WebhookService;
    webhooksRepository = module.get(getRepositoryToken(Webhook));
    usersRepository = module.get(getRepositoryToken(User));
    orgEnvRepo = module.get(getRepositoryToken(OrgEnv));
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('sendUserCreated', () => {
    it('should return added message id', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      await expect(
        service.sendChildActivated(
          {
            userId: 0,
            signUpCountry: 'IX',
          },
          1,
          orgEnvId,
        ),
      ).resolves.toEqual(0);
    });

    it('should return void if webhook not found', async () => {
      await expect(
        service.sendChildActivated(
          {
            userId: 0,
            signUpCountry: 'IX',
          },
          1,
          orgEnvId,
        ),
      ).resolves.toBeUndefined();
    });
  });

  describe('sendAccountGraduated', () => {
    let defaultUserGraduatedDto: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO>;

    beforeEach(() => {
      defaultUserGraduatedDto = {
        name: EWebhookName.SETTINGS_USER_GRADUATED,
        time: Date.now(),
        orgId: '111',
        payload: {
          userId: '1',
        },
      };
    });

    it('should return added message id', async () => {
      const expectedResult = 0;
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      await expect(service.sendAccountGraduated(defaultUserGraduatedDto)).resolves.toEqual(expectedResult);
    });

    it('should return void if webhook not found', async () => {
      await expect(service.sendAccountGraduated(defaultUserGraduatedDto)).resolves.toBeUndefined();
    });
  });

  describe('sendSettingsEffectiveValuesChanged', () => {
    let defaultSettingsValuesChanged: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO<boolean>>;

    beforeEach(() => {
      defaultSettingsValuesChanged = {
        name: EWebhookName.SETTINGS_EFFECTIVE_VALUES_CHANGED,
        time: Date.now(),
        orgId: '111',
        productId: '123',
        payload: {
          userId: '4334',
          settings: [
            {
              namespace: 'default',
              settingName: 'dataShare',
              effectiveValue: true,
              effectiveSource: 'source',
              trigger: 'trigger',
            },
          ],
        },
      };
    });

    it('does not send kafka message if webhook is null', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(null);

      await expect(
        service.sendSettingsEffectiveValuesChanged(defaultSettingsValuesChanged, orgEnvId),
      ).resolves.toBeUndefined();

      expect(mockKafka.sendMessage).not.toHaveBeenCalled();
    });

    it('should send expected dto to kafka', async () => {
      const expectedResult = 0;
      const externalUserId = '24';

      const mockedUser = {
        externalId: externalUserId,
        activations: [],
        language: 'it',
        orgEnv: {} as OrgEnv,
        orgEnvId: '',
        uuid: '',
        id: +defaultSettingsValuesChanged.payload.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as unknown as User;

      const mockedOrgEnv = {
        id: orgEnvId,
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
      } as OrgEnv;

      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValueOnce(mockedOrgEnv);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(mockedUser);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);

      jest.spyOn(service['settingsService'], 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'default',
          settingName: 'dataShare',
          effectiveValue: true,
        },
      ] as unknown as UserSettingValueDTO[]);

      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      const expectedPayload: UserPermissionChangedPayloadDTO = {
        userId: Number(defaultSettingsValuesChanged.payload.userId),
        userExternalId: +externalUserId,
        permissions: {
          dataShare: true,
        },
      };

      await service.sendSettingsEffectiveValuesChanged(defaultSettingsValuesChanged, orgEnvId);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.USER_PERMISSION_CHANGED,
          payload: expectedPayload,
        }),
      );
    });

    it.each(['24', null])('should send expected dto to kafka', async (externalUserId) => {
      const expectedResult = 0;

      const mockedUser = {
        externalId: externalUserId,
        activations: [],
        language: 'it',
        orgEnv: {} as OrgEnv,
        orgEnvId: '',
        uuid: '',
        id: +defaultSettingsValuesChanged.payload.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as unknown as User;

      const mockedOrgEnv = {
        id: orgEnvId,
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
      } as OrgEnv;

      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValueOnce(mockedOrgEnv);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(mockedUser);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);

      jest.spyOn(service['settingsService'], 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'default',
          settingName: 'dataShare',
          effectiveValue: true,
        },
      ] as unknown as UserSettingValueDTO[]);

      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      const expectedPayload: UserPermissionChangedPayloadDTO = {
        userId: Number(defaultSettingsValuesChanged.payload.userId),
        userExternalId: externalUserId ? Number(externalUserId) : undefined,
        permissions: {
          dataShare: true,
        },
      };

      await service.sendSettingsEffectiveValuesChanged(defaultSettingsValuesChanged, orgEnvId);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.USER_PERMISSION_CHANGED,
          payload: expectedPayload,
        }),
      );
    });

    it('should return void if webhook not found', async () => {
      const mockedOrgEnv = {
        id: orgEnvId,
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
      } as OrgEnv;

      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValueOnce(mockedOrgEnv);

      jest.spyOn(service['settingsService'], 'getUserSettings').mockResolvedValueOnce([
        {
          namespace: 'namespace',
          settingName: 'dataShare',
          effectiveValue: true,
        },
      ] as unknown as UserSettingValueDTO[]);

      await expect(
        service.sendSettingsEffectiveValuesChanged(defaultSettingsValuesChanged, orgEnvId),
      ).resolves.toBeUndefined();
    });

    it('should use SettingsService.classicValue when setting has a definition property', async () => {
      // Setup a copy of the default payload but with a setting that includes a definition property
      const payloadWithDefinition = {
        ...defaultSettingsValuesChanged,
        payload: {
          ...defaultSettingsValuesChanged.payload,
          settings: [
            {
              namespace: 'default',
              settingName: 'dataShare',
              effectiveValue: true,
              effectiveSource: 'source',
              trigger: 'trigger',
              definition: { type: 'boolean', default: false },
            },
          ],
        },
      };

      const mockedOrgEnv = {
        id: orgEnvId,
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
      } as OrgEnv;
      const mockedUser = { externalId: '24', id: 4334 } as User;

      jest.spyOn(orgEnvRepo, 'findOneByOrFail').mockResolvedValueOnce(mockedOrgEnv);
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(mockedUser);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      jest
        .spyOn(service['settingsService'], 'getUserSettings')
        .mockResolvedValueOnce([payloadWithDefinition.payload.settings[0]] as unknown as UserSettingValueDTO[]);

      const classicValueSpy = jest.spyOn(SettingsService, 'classicValue').mockReturnValue(false);
      jest.spyOn(SettingsService, 'settingToPermissionName').mockReturnValue('dataShare');

      await service.sendSettingsEffectiveValuesChanged(payloadWithDefinition, orgEnvId);

      expect(classicValueSpy).toHaveBeenCalledWith(expect.objectContaining({ definition: expect.any(Object) }));

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          payload: expect.objectContaining({
            permissions: { dataShare: false },
          }),
        }),
      );
    });
  });

  describe('sendUserAccountDeleted', () => {
    it('should return added message id', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      await expect(
        service.sendUserAccountDeleted(
          {
            userId: 0,
          },
          '111',
        ),
      ).resolves.toEqual(0);
    });

    it('should return void if webhook not found', async () => {
      await expect(
        service.sendUserAccountDeleted(
          {
            userId: 0,
          },
          '111',
        ),
      ).resolves.toBeUndefined();
    });
  });

  describe('sendUserAddedToFamily', () => {
    let defaultUserAddedToFamilyDto: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO>;

    beforeEach(() => {
      defaultUserAddedToFamilyDto = {
        name: EWebhookName.FAMILIES_USER_ADDED_TO_FAMILY,
        time: Date.now(),
        orgId: '111',
        payload: {
          userId: '1',
        },
      };
    });

    it('should return added message id', async () => {
      const expectedResult = 0;
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      await expect(service.sendUserAddedToFamily(defaultUserAddedToFamilyDto)).resolves.toEqual(expectedResult);
    });

    it('should send expected dto to kafka', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      const userId = Number(defaultUserAddedToFamilyDto.payload.userId);

      const expectedPayload: ChildLinkedToParentPayloadDTO = {
        userId: userId,
        parentId: userId,
      };

      await service.sendUserAddedToFamily(defaultUserAddedToFamilyDto);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.CHILD_LINKED_TO_PARENT,
          payload: expectedPayload,
        }),
      );
    });

    it('should return void if webhook not found', async () => {
      await expect(service.sendUserAddedToFamily(defaultUserAddedToFamilyDto)).resolves.toBeUndefined();
    });
  });

  describe('sendUserRemovedFromFamily', () => {
    let defaultUserRemovedFromFamilyDto: OrgLevelWebhookPayload<FamiliesUserRemovedFromFamilyDTO>;

    beforeEach(() => {
      defaultUserRemovedFromFamilyDto = {
        name: EWebhookName.FAMILIES_USER_REMOVED_FROM_FAMILY,
        time: Date.now(),
        orgId: '111',
        payload: {
          userId: '1',
        },
      };
    });

    it('should return added message id', async () => {
      const expectedResult = 0;
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      await expect(service.sendUserRemovedFromFamily(defaultUserRemovedFromFamilyDto)).resolves.toEqual(expectedResult);
    });

    it('should return void if webhook not found', async () => {
      await expect(service.sendUserRemovedFromFamily(defaultUserRemovedFromFamilyDto)).resolves.toBeUndefined();
    });
  });

  describe('sendChildActivationDeletedDeleted', () => {
    it('should return added message id', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      await expect(
        service.sendChildActivationDeleted(
          {
            signUpCountry: '',
            userId: 0,
          },
          8,
          orgEnvId,
        ),
      ).resolves.toEqual(0);
    });

    it('should send expected dto to kafka', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      const expectedPayload: ChildActivationDeletedPayloadDTO = {
        signUpCountry: '',
        userId: 0,
      };

      await service.sendChildActivationDeleted(expectedPayload, 8, orgEnvId);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.CHILD_ACTIVATION_DELETED,
          payload: expectedPayload,
        }),
      );
    });

    it('should return void if webhook not found', async () => {
      await expect(
        service.sendChildActivationDeleted(
          {
            signUpCountry: '',
            userId: 0,
          },
          8,
          orgEnvId,
        ),
      ).resolves.toBeUndefined();
    });
  });
  describe('sendGuardianRequestExpired', () => {
    let defaultGuardianRequestExpiredDto: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto>;

    beforeEach(() => {
      defaultGuardianRequestExpiredDto = {
        name: EWebhookName.FAMILIES_GUARDIAN_REQUEST_EXPIRED,
        time: Date.now(),
        orgId: '111',
        payload: {
          userId: '1',
          guardianRequestId: '2f5867b5-76fd-4ee1-ac48-10311a665c54',
        },
      };
    });

    it('should return added message id', async () => {
      const expectedResult = 0;
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(expectedResult);

      await expect(service.sendGuardianRequestExpired(defaultGuardianRequestExpiredDto)).resolves.toEqual(
        expectedResult,
      );
    });

    it('should return void if webhook not found', async () => {
      await expect(service.sendGuardianRequestExpired(defaultGuardianRequestExpiredDto)).resolves.toBeUndefined();
    });

    it('should send expected dto to kafka', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);

      const userIds = [Number(defaultGuardianRequestExpiredDto.payload.userId)];

      const expectedPayload: UnresponsiveParentAccountDeletedPayloadDto = {
        userIds: userIds,
      };

      await service.sendGuardianRequestExpired(defaultGuardianRequestExpiredDto);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.UNRESPONSIVE_PARENT_ACCOUNT_DELETED,
          payload: expectedPayload,
        }),
      );
    });
  });

  describe('sendFamilyGroupDeleted', () => {
    let defaultPayload: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto>;
    let parentUserId: string;
    let memberUserId: string;

    beforeEach(() => {
      parentUserId = '********';
      memberUserId = '********';

      defaultPayload = {
        name: EWebhookName.FAMILIES_USER_REMOVED_FROM_FAMILY,
        time: Date.now(),
        orgId: '111',
        payload: {
          familyGroupId: '',
          orgEnvId: '',
          orgId: '',
          members: [
            {
              id: '4234',
              userId: parentUserId,
              role: EMemberRole.Manager,
              email: '<EMAIL>',
            },
            {
              id: '4234',
              userId: memberUserId,
              role: EMemberRole.Supervised,
              email: '<EMAIL>',
            },
          ],
        },
      };
    });

    it('should return added message ids', async () => {
      const messageId = 20;
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);

      mockKafka.sendMessage.mockResolvedValueOnce(messageId);

      await expect(service.sendFamilyGroupDeleted(defaultPayload)).resolves.toEqual([messageId]);
    });

    it('should send expected dto to kafka', async () => {
      jest.spyOn(webhooksRepository, 'findOne').mockResolvedValueOnce(mockedWebhook);
      mockKafka.sendMessage.mockResolvedValueOnce(0);
      const expectedPayload: ParentAccountDeletedPayloadDto = {
        parentId: Number(memberUserId),
      };

      await service.sendFamilyGroupDeleted(defaultPayload);

      expect(mockKafka.sendMessage).toHaveBeenCalledWith(
        'default',
        expect.objectContaining({
          name: ClassicWebhookName.PARENT_ACCOUNT_DELETED,
          payload: expectedPayload,
        }),
      );
    });

    it('should return void if webhook not found', async () => {
      await expect(service.sendFamilyGroupDeleted(defaultPayload)).resolves.toEqual([undefined]);
    });
  });
});

# Classic vs Wrapper ID Comparison Scripts

This directory contains two TypeScript scripts that compare user and activation IDs between Classic and Wrapper PostgreSQL databases.

## Overview

The scripts extract IDs from both Classic and Wrapper databases for a hard-coded list of customers, compute differences, and write any discrepancies to text files in `./shared/`.

## Scripts

- `compare-user-ids.ts` - Compares user IDs between Classic and Wrapper databases
- `compare-activation-ids.ts` - Compares activation IDs between Classic and Wrapper databases

## Setup

1. **Configure customers**: Edit `customers.ts` to define your customer list:
   ```typescript
   export const customers: Customer[] = [
     {
       name: 'customer-name',
       classicDb: 'classic_database_name',
       orgEnvId: 'wrapper-org-env-id'
     }
   ];
   ```

2. **Set environment variables**: Copy `comparison.env.example` to `comparison.env` and configure:
   ```bash
   CLASSIC_DATABASE_URL=postgresql://user:password@localhost:5432
   WRAPPER_DATABASE_URL=postgresql://user:password@localhost:5433/wrapper_db
   BEFORE_DATE=2024-01-01T00:00:00Z
   ```

## Usage

Run the scripts using npm:

```bash
# Compare user IDs
npm run compare-user-ids

# Compare activation IDs  
npm run compare-activation-ids
```

## Output

The scripts create a `./shared/` directory and write difference files when discrepancies are found:

- `${customer-name}-user-ids-in-classic-missing-in-wrapper`
- `${customer-name}-user-ids-in-wrapper-missing-in-classic`
- `${customer-name}-activation-ids-in-classic-missing-in-wrapper`
- `${customer-name}-activation-ids-in-wrapper-missing-in-classic`

Each file contains one numeric ID per line, sorted ascending, UTF-8 encoded with LF line endings.

## Requirements

- Node 18+
- PostgreSQL access to both Classic and Wrapper databases
- Environment variables properly configured
- Customer list defined in `customers.ts`

## Processing Flow

For each customer in the array:

1. Connect to Classic database (`CLASSIC_DATABASE_URL` + `classicDb`)
2. Fetch IDs using Classic queries (filtered by `BEFORE_DATE`)
3. Connect to Wrapper database (`WRAPPER_DATABASE_URL`)
4. Fetch IDs using Wrapper queries (filtered by `orgEnvId` and `BEFORE_DATE`)
5. Compute set differences
6. Write non-empty difference sets to files in `./shared/`
7. Log progress and results

The scripts process customers sequentially and exit with code 0 on success or non-zero on failure. 
import { Injectable } from '@nestjs/common';
import {
  AuthLibraryUtils,
  EKeycloakScope,
  KeycloakService as KeycloakClient,
} from '@superawesome/freekws-auth-library';
import { SALogger } from '@superawesome/freekws-common-logger';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import { Span } from 'nestjs-ddtrace';

import { IClientCredentials } from '../../../org-env/types';
import { ConfigService } from '../config/config.service';

const tokens = new Map<string, string>();

@Injectable()
@Span()
export class ClientKeycloakService {
  private readonly clients = new Map<string, KeycloakClient>();
  private readonly logger: SALogger;

  constructor(private readonly configService: ConfigService, private readonly metricsService: MetricsService) {
    this.logger = new SALogger();
  }

  async getClientToken({ clientId, secret }: IClientCredentials) {
    let token = tokens.get(clientId);

    if (!token || !AuthLibraryUtils.isTokenValid(token)) {
      let client = this.clients.get(clientId);
      if (!client) {
        client = new KeycloakClient(
          { ...this.configService.getKeycloak(), clientId, secret },
          this.logger,
          this.metricsService,
        );
        this.clients.set(clientId, client);
      }

      token = await client.getScopedAccessToken([
        EKeycloakScope.AGE_GATE,
        EKeycloakScope.SETTINGS,
        EKeycloakScope.FAMILY,
        EKeycloakScope.CONSENT,
      ]);
      tokens.set(clientId, token);
    }

    return token;
  }
}

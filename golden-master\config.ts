import { configDotenv } from 'dotenv';
import * as path from 'node:path';

configDotenv({ path: path.join(__dirname, '.env') });

import chalk from 'chalk';
import { testData } from './test-data';
import { userManagementTests } from './test-cases/user-management';
import { authenticationTests } from './test-cases/authentication';
import { appConfigTests } from './test-cases/app-config';
import { workflowTests } from './test-cases/workflows';

export type Prettify<T> = {
  [K in keyof T]: T[K];
} & {};

export type BodyFieldConditions = Record<string, 'skip' | 'present' | 'redacted'>;

export interface EndpointConfig {
  name?: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  params?: Record<string, string>;
  payload?: any;
  headers?: Record<string, string>;
  bodyFieldConditions?: BodyFieldConditions;
  skipHeaders?: string[];
  only?: boolean; // Used to skip any other endpoint that doesn't have only set to true
  workflow?: never;
  useAuth?: 'javiTest' | 'domCraft';
  user?: {
    username: string;
    password: string;
  },
  sortKey?: string; // Sorts array responses by the specified key
  contentType?: 'application/json' | 'application/x-www-form-urlencoded'; // Content type for the request
  skipAuth?: boolean; // Skip adding Authorization header for OAuth token requests
}

export type WorkflowEndpointConfig = Prettify<Omit<EndpointConfig, WorkflowLiftUpElements | 'workflow'> & {
  workflowState?: any,
}>

type WorkflowLiftUpElements = 'only' | 'name';

export type StepData = {
  resBody: any;
  params: any;
  prevWorkflowState: any;
};

export type WorkflowConfig = Prettify<Pick<EndpointConfig, WorkflowLiftUpElements> & {
  workflow: ((response: StepData) => WorkflowEndpointConfig)[];
}>

export interface TestConfig {
  classicApi: string;
  wrapperApi: string;
  auth: {
    javiTest: {
      wrapperClientId: string;
      wrapperClientSecret: string;
      classicOauthUsername: string;
      classicOauthPassword: string;
    };
    domCraft: {
      wrapperClientId: string;
      wrapperClientSecret: string;
      classicOauthUsername: string;
      classicOauthPassword: string;
    };
  };
  endpoints: (EndpointConfig | WorkflowConfig)[];
  defaultParams?: Record<string, string>;
  logFullResponses?: boolean;
}


function getBaseUrl(baseUrl: string) {
  if (baseUrl.endsWith('/')) {
    return baseUrl.slice(0, -1);
  }
  return baseUrl;
}

const domCraftAppId = testData.apps.domCraft;
const javiTestAppId = testData.apps.javiTest;

export const config: TestConfig = {
  classicApi: getBaseUrl(process.env.CLASSIC_API || 'http://localhost:84'),
  wrapperApi: getBaseUrl(process.env.WRAPPER_API || 'http://localhost:85'),
  auth: {
    javiTest: {
      wrapperClientId: process.env.WRAPPER_CLIENT_ID || 'not-provided',
      wrapperClientSecret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
      classicOauthUsername: process.env.CLASSIC_OAUTH_USERNAME || 'not-provided',
      classicOauthPassword: process.env.CLASSIC_OAUTH_PASSWORD || 'not-provided',
    },
    domCraft: {
      wrapperClientId: process.env.DOM_CRAFT_WRAPPER_CLIENT_ID || 'not-provided',
      wrapperClientSecret: process.env.DOM_CRAFT_WRAPPER_CLIENT_SECRET || 'not-provided',
      classicOauthUsername: process.env.DOM_CRAFT_CLASSIC_OAUTH_USERNAME || 'not-provided',
      classicOauthPassword: process.env.DOM_CRAFT_CLASSIC_OAUTH_PASSWORD || 'not-provided',
    },
  },
  logFullResponses: false,
  defaultParams: {
    // default params for all endpoints in path
    appId: javiTestAppId,
  },
  endpoints: [
    ...workflowTests,
    ...userManagementTests,
    ...authenticationTests,
    ...appConfigTests,
  ],
};

if (config.endpoints.some((endpoint) => endpoint.only)) {
  console.log(chalk.yellow('🚨 Some endpoint(s) are set to only. Skipping all other endpoints.'));
  const totalEndpoints = config.endpoints.length;
  config.endpoints = config.endpoints.filter((endpoint) => endpoint.only);
  const filteredEndpoints = config.endpoints.length;
  console.log(chalk.yellow(`🚨 Skipped ${totalEndpoints - filteredEndpoints} endpoint(s) / workflow(s).`));
}
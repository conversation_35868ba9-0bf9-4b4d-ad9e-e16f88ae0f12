import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOauthCallbackUrlTable1741600000000 implements MigrationInterface {
    name = 'AddOauthCallbackUrlTable1741600000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "oauth_callback_url"
            (
                "id"        SERIAL            NOT NULL,
                "appId"     integer           NOT NULL,
                "orgEnvId"  character varying NOT NULL,
                "url"       character varying NOT NULL,
                "createdAt" TIMESTAMP         NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP         NOT NULL DEFAULT now(),
                "deletedAt" TIMESTAMP,
                CONSTRAINT "PK_oauth_callback_url_id" PRIMARY KEY ("id")
            )
        `);

        await queryRunner.query(`
            ALTER TABLE "oauth_callback_url"
                ADD CONSTRAINT "FK_7ebc49ad9f58a2a51ecb4fc53e6"
                    FOREIGN KEY ("appId", "orgEnvId")
                        REFERENCES "app" ("id", "orgEnvId")
                        ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "oauth_callback_url"`);
    }
}

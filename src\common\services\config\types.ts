import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

import type { ConfigService } from './config.service';

export type TDbCli = {
  migrationsDir: string;
};

export type TDbConfig = {
  type: string;
  masterUrl: string;
  slaveUrls: string;
  synchronize: boolean;
  logging: boolean;
  entities: string[];
  subscribers: string[];
  autoLoadEntities: boolean;
  migrations: string[];
  cli: TDbCli;
  maxConnectionLimit: string;
};

export type TIntervalHealthCheck = {
  intervalMs: number;
};

export type TAppHttpConfig = {
  port: number;
  cors?: CorsOptions;
};

export type TKeycloakConfig = {
  realm: string;
  clientId: string;
  secret: string;
  authServerUrl: string;
  timeoutMs: number;
  audience?: string;
  expirationTime?: number;
  realmUrl?: string;
  additionalTrustedIssuers?: string[];
};

export type TCircuitManagerOptions = {
  timeoutMs: number;
  errorThresholdPercentage: number;
  resetTimeoutMs: number;
  errorFilter?: (e: Error) => boolean;
};

export type TServiceConfig = {
  baseURL: string;
  timeoutMs: number;
  retries: number;
  initialRetryDelay: number;
  bailOnStatus: number[];
};

export type TCacheableConfig = {
  enabled: boolean;
  defaultTtlSecs: number;
  maxCacheSizeEntries: number;
  metricsNamespace: string;
};

export type TKafkaConfig = {
  kafkaHost: string;
  outOfOrderTopic: string;
};

export type TAnalyticConfig = {
  baseURL: string;
  upstream: string;
  authorizationHeader: string;
};

export type TAppWebhooksConfig = {
  topicName: string;
  sendWebhooks: boolean;
};

export type TEncryptionConfig = { secrets: string[]; secretVersion: number };

export type BadgerConfig = {
  apiKey: string;
  baseURL: string;
};

export type TalonConfig = {
  apiKey: string;
  flowId: string;
};

export type TEmailServiceConfig = {
  kafkaTopic: string;
  footer: {
    logoSrc: string;
  };
  header: {
    headerSrc: string;
  };
};

export type TLinksConfig = {
  kwsFaqLink: string;
  kwsPrivacyPolicyLink: string;
  kwsTermsLink: string;
  kwsHelpCentreLink: string;
};

export type TCommonApiClientConfig = {
  baseURL: string;
  timeoutMs: number;
  retries: number;
  initalRetryDelay: number;
  bailOnStatus: number[];
};

export type TNianticConfig = {
  frontendAuthUrl: string;
  appDisplayName: string;
  appClientId: string;
  resetPwdTokenExpiryTimeInHours: number;
  supportEmailAddress: string;
};

export type TAppConfig = ReturnType<ConfigService['getConfig']>;

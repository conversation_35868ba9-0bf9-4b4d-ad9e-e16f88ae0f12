# Golden Master Testing Suite

## Introduction: What is this and Why Does it Exist?

This test suite is a safety net designed to ensure that the new **Wrapper API** behaves identically
to the legacy **Classic API** (sa-kws-api). Its primary purpose is to guarantee that API responses
remain
consistent between the two systems, preventing breaking changes for any client applications that
rely on them.

By running the same requests against both APIs and comparing the results,
we can be more confident that functional parity is maintained.

> **Important Note**: These tests are **not** part of any automated CI/CD pipeline. They must be run
> manually before deploying any changes that could potentially alter API response data.

---

## Quick Start Guide

### 1. Environment Setup

The test suite uses a `.env` file to manage environment-specific variables like API endpoints and
credentials.

First, navigate to the `golden-master` directory and create your own `.env` file by copying the
example file:

```bash
cp golden-master/example.env golden-master/.env
```

Next, open `golden-master/.env` and fill in the correct values. The file contains two sets of
credentials, one for a default test app (javi-test) and one for the "Dom Craft" app. These
credentials are used in authenticating requests to the two APIs.

- `CLASSIC_API`: The base URL for the legacy Classic API.
- `WRAPPER_API`: The base URL for the new Wrapper API.
- `WRAPPER_CLIENT_ID` / `WRAPPER_CLIENT_SECRET`: Credentials for the Wrapper API.
- `CLASSIC_OAUTH_USERNAME` / `CLASSIC_OAUTH_PASSWORD`: Credentials for the Classic API.
- `DOM_CRAFT_*`: Credentials for the second test application, used in certain workflows.

> *Note: Credentials for apps can be found in the database, in the `apps` table*
>
> *oauthClientId => username, apiKey => password*

### 2. Running the Tests

To execute the entire test suite, run the following command from the **root of the project**:

```bash
npm run golden-master:run-no-input
```

---

## How to Write and Edit Tests

Tests are defined as objects in the files within the `golden-master/test-cases/` directory. You can
add new tests or edit existing ones there.

### Test Types

There are two kinds of tests you can write:

1. **Single Endpoint Tests**: These are simple, one-off checks of a specific API endpoint. They are
   ideal for testing GET requests or simple POSTs that don't depend on other actions.
2. **Workflow Tests**: These are for more complex, multi-step scenarios where one API call depends
   on the data from a previous one (e.g., creating a user, then using that user's ID to update their
   permissions).

### Configuration Options

Each test is an object with several available configuration keys:

| Key                   | Description                                                                                                       |
|-----------------------|-------------------------------------------------------------------------------------------------------------------|
| `name`                | A descriptive name for the test, which is printed in the console.                                                 |
| `path`                | The endpoint path (e.g., `/v2/users/:userId`). Dynamic params like `:userId` are replaced by the `params` object. |
| `method`              | The HTTP method (`GET`, `POST`, etc.).                                                                            |
| `params`              | An object containing values for any dynamic segments in the `path`.                                               |
| `payload`             | The request body for `POST`, `PUT`, or `PATCH` requests.                                                          |
| `useAuth`             | Specifies which set of credentials to use: `'javiTest'` (default) or `'domCraft'`.                                |
| `user`                | An object with `{ username, password }` for making user-level authenticated requests instead of app-level tokens. |
| `skipAuth`            | Set to `true` to skip adding the `Authorization` header. Used for public endpoints or token requests.             |
| `headers`             | An object for any custom request headers.                                                                         |
| `skipHeaders`         | An array of header names to ignore during response comparison.                                                    |
| `contentType`         | The `Content-Type` of the request. Defaults to `application/json`.                                                |
| `only`                | Set to `true` to run only this test. See [Isolating a Single Test](#isolating-a-single-test).                     |
| `sortKey`             | See [Handling Dynamic Data](#handling-dynamic-data).                                                              |
| `bodyFieldConditions` | See [Handling Dynamic Data](#handling-dynamic-data).                                                              |

### Dynamic Data in Payloads

You can use `{random}` placeholders in payload strings to generate unique values:

```typescript
payload = {
  email: 'user-{random}@example.com',  // Becomes '<EMAIL>'
  username: 'testuser-{random}'        // Becomes 'testuser-xyz789'
}
```

This is useful for creating unique usernames, emails, or other identifiers in tests.

### Handling Dynamic Data

API responses often contain dynamic data (like unique IDs, dates or encrypted values) that will
always be
different between two calls. The test suite provides two mechanisms to handle this:

**`sortKey`**
If a response is an array of objects, its order is not guaranteed. Use `sortKey` to specify a field
by which to sort the array, ensuring a consistent order for comparison.

```typescript
// Sorts the array of permissions by the 'name' field before comparing
config = {
    name: 'Get Translation Permissions',
    path: '/v2/apps/:appId/permissions/translated',
    method: 'GET',
    sortKey: "name",
}
```

**`bodyFieldConditions`**
This object lets you define rules for specific fields in the response body.

- `'skip'`: **Removes the field** from both responses. Use this for values that are always unique
  and
  that you don't care is in the response or not.

  ```typescript
  bodyFieldConditions: { id: 'skip', uuid: 'skip' }
  ```

- `'present'`: **Checks for the field's existence** but ignores its value. The value is replaced
  with a static placeholder (`'isPresentComparisonValue'`). Use this when you care that a field is
  returned, but not what its specific value is.

  ```typescript
  bodyFieldConditions: { 'user.email': 'present' }
  ```

- `'redacted'`: **Replaces the field's value** with `[REDACTED]` in the Classic API's response only.
  This is for some special fields in the wrapper that we have replaced with `[REDACTED]` to
  prevent leaking sensitive data. Classic would have otherwise returned the actual value.

  ```typescript
  bodyFieldConditions: { 'legacyField': 'redacted' }
  ```

**A Note on Pathing**

The keys for `bodyFieldConditions` use `lodash`-style pathing, not full JSONPath. This means you can
use dot notation to access nested properties (e.g., `'user.profile.email'`).

However, there are limitations to be aware of:

- **No Array Indexing**: You cannot target specific elements in an array, e.g., `'users[0].name'`.
- **`skip` on Top-Level Array Fields**: The `skip` condition uses `lodash.omit`, which converts
  arrays to objects if you try to skip a field on an object within an array. It is best used for
  top-level fields. For nested fields, prefer using `'present'` or `'redacted'` where possible.
- **No Wildcards or Queries**: Advanced JSONPath features like wildcards (`*`), deep recursive
  search (`..`), or filter expressions (`?(@.price < 10)`) are not supported.

### Workflow-Specific Features

For multi-step workflow tests, you have additional options:

**`workflowState`**
Pass custom data between workflow steps:

```typescript
// Step 1: Store user data for later steps
step1 = {
  workflowState: {
    userId: resBody.id,
    userEmail: '<EMAIL>'
  }
}

// Step 2: Access previous state
step2 = ({ prevWorkflowState }) => ({
  path: '/v2/users/:userId',
  params: {
    userId: prevWorkflowState.userId  // Use stored data
  }
})
```

**Individual Step Names**
Each workflow step can have its own `name` for clearer logging:

```typescript
({ resBody }) => ({
  name: "Activate user with second app",  // Shows in console output
  path: '/v2/apps/:appId/users/:userId/activate',
  // ...
})
```

### Authentication

The test suite supports two types of authentication:

**App-Level Authentication (Default)**
Uses client credentials to get an app token. This is the default behavior:

```typescript
config = {
  name: 'Get App Config',
  path: '/v2/apps/:appId/config',
  method: 'GET',
  useAuth: 'javiTest'  // Uses app credentials
}
```

**User-Level Authentication**
Uses a specific user's username and password to get a user token:

```typescript
config = {
  name: 'Get User Profile',
  path: '/v2/users/:userId',
  method: 'GET',
  user: {
    username: 'testuser123',
    password: 'password123'
  }
}
```

The test suite automatically handles token generation and caching for both types.

### Test Data and Utilities

The test suite provides some built-in helpers:

- **Test Data**: Pre-defined app IDs and user IDs are available in `test-data.ts`
- **User Creation**: Use `createUserStatic(appId)` to generate test user objects

---

## Common Maintenance and Debugging

### Isolating a Single Test

When writing or debugging a test, it's helpful to run it in isolation. You can do this by adding
`only: true` to the test's configuration object. When the suite detects this flag, it will skip all
other tests.

```typescript
// This will be the only test that runs
config = {
    only: true,
    name: 'Get User by ID',
    path: '/v2/users/:userId',
    // ...
}
```

### Debugging Failing Tests

If a test is failing and the console output isn't enough, you can:

1. **Isolate the test** using the `only: true` flag as described above.
2. **Enable full response logging**. In `golden-master/config.ts`, set `logFullResponses: true`.

This will print the complete, sanitized response bodies from both the Classic and Wrapper APIs,
making it much easier to spot differences. The suite also shows the full request URL for failed
tests.

### Global Configuration

The main configuration file, `config.ts`, contains a few global settings:

- `logFullResponses`: Set to `true` to enable the debugging workflow described above.
- `defaultParams`: An object containing params that will be applied to all endpoints. This is useful
  for values like a default `appId` that is used in most requests.

### Response Processing

The test suite automatically handles several aspects of response comparison:

**Header Filtering**
These are headers that are always ignored between requests. See the sanitizeResponse function in
[test-utils.ts](https://github.com/SuperAwesomeLTD/freekws-classic-wrapper-backend/blob/main/golden-master/test-utils.ts)
for the full list.

**Additional Headers**
The test suite also adds `x-forwarded-host: kwsapi.v1staging.kidswebservices.com` to all requests
automatically, as this is required for every wrapper request to associated it with the correct
OrgEnv.
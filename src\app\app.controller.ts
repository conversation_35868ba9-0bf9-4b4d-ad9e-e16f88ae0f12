import {
  BadRequestException,
  Body,
  ConflictException,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiExtraModels,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  AppUserParams,
  DATE_FORMAT_REGEX,
  EOAuthScope,
  GetUserResponseDTO,
  RequestUserPermissionsDTO,
  RequestUserPermissionsResponseDTO,
  UpdateParentEmailDTO,
  UserCreateDTO,
  UserCreateResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { Public } from '@superawesome/freekws-nestjs-guards';
import { plainToInstance } from 'class-transformer';
import { Span } from 'nestjs-ddtrace';

import {
  AppConfigParams,
  AppsUsersActivateInputDTO,
  GetUserPermissionsResponseDTO,
  GetUserPermissionsResponseExtendedDTO,
} from './app.dto';
import { AppService } from './app.service';
import { ExtractOrgEnv } from '../common/guards/inject-org-env/inject-org-env.decorator';
import { InjectOrgEnvGuard } from '../common/guards/inject-org-env/inject-org-env.guard';
import { OauthGuard } from '../common/guards/oauth/oauth.guard';
import { And, HasScope, IsOwnApp, Policies } from '../common/guards/policies';
import { SettingsService } from '../common/services/settings/settings.service';
import { EAPITags } from '../common/types';
import { OrgEnv } from '../org-env/org-env.entity';
import { UserService } from '../user/user.service';

const INNERSLOTH_ORG_ENV_ID = '8080e2b3-4f65-47b7-bd67-df01913a9a0d';

@Controller()
@UseGuards(InjectOrgEnvGuard)
@ApiExtraModels(UserCreateDTO, UserCreateResponseDTO)
@Span()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly userService: UserService,
    private readonly logger: SALogger,
  ) {
  }

  @ApiOperation({
    summary: 'Create a user account and activates the given app for the user',
    description:
      'This endpoint creates a new user account and activates the app, sending a consent email to the parent if user is under of digital consent age.',
  })
  @ApiTags(EAPITags.AppsUsers)
  @ApiOkResponse({
    type: UserCreateResponseDTO,
  })
  @Post('/v2/apps/:appId/users')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(OauthGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP, EOAuthScope.MOBILE_APP), new IsOwnApp()],
  })
  @Span()
  async createUser(
    @Param('appId', { transform: Number }) appId: number,
    @Body() body: UserCreateDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ): Promise<UserCreateResponseDTO> {
    const dateOfBirth = body.dateOfBirth;

    // Customer-specific DOB validation bypass configuration. For customers who have anonymousUsers set in classic
    // These customers can pass undefined dateOfBirth, but if provided, it must still be valid format
    const DOB_OPTIONAL_CUSTOMERS: Record<string, number[]> = {
      'b56a1ed8-f0ad-4d44-9344-87bedbe4211d': [*********], // beme-health (added test fixture ID)
      'fbfc3b0e-6f25-4653-9699-aadcd296292e': [*********], // faa
      '6a1c3c47-84f7-42e4-bed6-46066fde0d90': [209179154], // hutch
      '644ec6f6-7b0f-4c57-b2c3-7dda9ee3c9a4': [
        *********, 279130382, 256387300, 349098747, 302757913, 372709165, 209179154, 325897636,
      ], // milestone
      '00ffea64-80a7-4a4a-a2bb-9f085739cf1d': [256387300, 279130382, 209179154, *********], // passion sports
    };

    const isOptionalDobCustomer = DOB_OPTIONAL_CUSTOMERS[orgEnv.id]?.includes(appId) ?? false;

    if (dateOfBirth) {
      if (!DATE_FORMAT_REGEX.test(dateOfBirth)) {
        throw new BadRequestException('dateOfBirth is required and must be in YYYY-MM-DD format');
      }
    } else if (!isOptionalDobCustomer) {
      throw new BadRequestException('dateOfBirth is required and must be in YYYY-MM-DD format');
    }
    const result = await this.appService.registerUser(
      orgEnv.id,
      {
        ...body,
        dateOfBirth,
        // Slight deviation from classic behaviour. Classic stores invalid country codes in the database,
        // here we store ZZ by default if given an invalid country code,
        // otherwise most FreeKWS APIs throw 400's it seems.
        country: SettingsService.convertUnsupportedCountryCode(body.country),
      },
      appId,
    );

    return plainToInstance(UserCreateResponseDTO, result, { excludeExtraneousValues: true });
  }

  @ApiOperation({
    summary: 'Request permissions',
    description: `Triggers a permission request by email to the parent of the user.
    A permission must first be set up and configured for your application; any user
    authenticated in the application can then request the permission.`,
  })
  @ApiTags(EAPITags.AppsUsers)
  @ApiOkResponse({
    type: RequestUserPermissionsResponseDTO,
  })
  @Post('/v2/apps/:appId/users/:userId/request-permissions')
  @HttpCode(HttpStatus.OK)
  @UseGuards(OauthGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async updateUserPermissions(
    @Param() { appId, userId }: AppUserParams,
    @Body() body: RequestUserPermissionsDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ): Promise<RequestUserPermissionsResponseDTO> {
    if (orgEnv.id === INNERSLOTH_ORG_ENV_ID) {
      try {
        const userActivated = await this.userService.userActivatedForApp(userId, appId, orgEnv.id);
        if (!userActivated) {
          await this.userService.activateUserToApp(userId, appId, body, orgEnv.id);
        }
      } catch (error) {
        this.logger.error('Error in activating Innersloth app special case', error);
      }
    }

    const { permissions } = body;
    if (permissions === undefined || permissions.length === 0) {
      const user = await this.userService.getUser(userId, orgEnv.id);
      if (!user) {
        throw new NotFoundException(`User ID ${userId} not found`);
      }

      return plainToInstance(
        RequestUserPermissionsResponseDTO,
        { user },
        {
          excludeExtraneousValues: true,
          exposeDefaultValues: true,
        },
      );
    }

    const userActivated = await this.userService.userActivatedForApp(userId, appId, orgEnv.id);
    if (!userActivated) {
      throw new NotFoundException('User not activated for the provided app');
    }

    const { user, userSettings } = await this.appService.requestPermissionsForUser(orgEnv.id, {
      ...body,
      appId,
      userId,
      permissions,
    });

    const userSettingsWithoutTermsAndConditions = SettingsService.removeTermsAndConditionsSetting(userSettings);
    const resultPermissions = SettingsService.transformSettingsToGroupedPermissions(
      userSettingsWithoutTermsAndConditions,
      permissions,
      user.language,
    );

    const response = {
      user: {
        ...user,
        parentEmail: '[REDACTED]',
      },
      permissions: SettingsService.transformSettingsToPermissions(userSettingsWithoutTermsAndConditions, permissions),
      resultPermissions,
    };

    return plainToInstance(RequestUserPermissionsResponseDTO, response, {
      excludeExtraneousValues: true,
      exposeDefaultValues: true,
    });
  }

  @ApiOperation({
    summary: "Update the user's parent's email",
    description:
      'Updates the parent email address attached to a user record, while clearing any opt-in permissions (& granted PII) attached to the account.',
  })
  @ApiTags(EAPITags.AppsUsers)
  @ApiNoContentResponse()
  @Post('/v2/apps/:appId/users/:userId/update-parent-email')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(OauthGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async updateParentEmail(
    @Param() { appId, userId }: AppUserParams,
    @Body() { parentEmail }: UpdateParentEmailDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ) {
    await this.appService.updateParentEmail(orgEnv.id, { appId, userId, parentEmail });
  }

  @ApiOperation({
    summary: 'Get the details of the given app/user',
    description: 'Returns details of the given app and user.',
  })
  @ApiTags(EAPITags.AppsUsers)
  @ApiOkResponse({ type: GetUserResponseDTO })
  @Get('/v2/apps/:appId/users/:userId')
  @HttpCode(HttpStatus.OK)
  @UseGuards(OauthGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async getUser(@Param() params: AppUserParams, @ExtractOrgEnv() orgEnv: OrgEnv): Promise<GetUserResponseDTO> {
    const result = await this.appService.getUser(orgEnv.id, params);

    return plainToInstance(GetUserResponseDTO, result, {
      excludeExtraneousValues: true,
      exposeDefaultValues: true,
    });
  }

  @ApiOperation({
    summary: 'Get user permissions for a specified app',
    description: 'Retrieves the permissions for a specific user for a specified app',
  })
  @ApiExtraModels(GetUserPermissionsResponseDTO, GetUserPermissionsResponseExtendedDTO)
  @ApiOkResponse({
    status: 200,
    schema: {
      oneOf: [
        {
          description: 'Flat permissions response (when extended is not provided)',
          $ref: getSchemaPath(GetUserPermissionsResponseDTO),
        },
        {
          description: 'Extended permissions response (when extended=true)',
          $ref: getSchemaPath(GetUserPermissionsResponseExtendedDTO),
        },
      ],
    },
    description: 'User permissions retrieved successfully',
  })
  @ApiQuery({
    name: 'extended',
    type: Boolean,
    required: false,
    description: 'When true, returns structured response with additional information',
  })
  @ApiTags(EAPITags.AppsUsers)
  @Get('/v2/apps/:appId/users/:userId/permissions')
  @UseGuards(OauthGuard)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async getUserPermissions(
    @Query('extended') extended: string,
    @Param('appId', new ParseIntPipe()) appId: number,
    @Param('userId', new ParseIntPipe()) userId: number,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ) {
    const { permissions, isMinor } = await this.appService.getUser(orgEnv.id, {
      appId,
      userId,
    });

    if (extended === 'true') {
      const isGraduated = !isMinor;
      return plainToInstance(
        GetUserPermissionsResponseExtendedDTO,
        {
          permissions: permissions,
          isGraduated,
        },
        { excludeExtraneousValues: true, exposeDefaultValues: true },
      );
    }

    return permissions;
  }

  @ApiOperation({
    summary: 'Delete app activation for specified user',
  })
  @ApiNoContentResponse()
  @Delete('/v2/apps/:appId/users/:userId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(OauthGuard)
  @ApiTags(EAPITags.AppsUsers)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async deleteUserActivation(
    @Param() { appId, userId }: AppUserParams,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ): Promise<void> {
    const userHasActivation = await this.appService.userHasActivation(orgEnv.id, userId, appId);
    if (!userHasActivation) {
      throw new NotFoundException('User is not activated for the given app.');
    }

    await this.userService.deleteActivation(orgEnv.id, userId, appId);
  }

  @ApiOperation({
    summary: 'Prompt a given user to review their permissions for a given app',
  })
  @Post('/v2/apps/:appId/users/:userId/review-permissions')
  @UseGuards(OauthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse()
  @ApiTags(EAPITags.AppsUsers)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP), new IsOwnApp()],
  })
  async reviewPermissions(@Param() { appId, userId }: AppUserParams, @ExtractOrgEnv() orgEnv: OrgEnv) {
    await this.appService.reviewPermissions(userId, appId, orgEnv.id);
    return {}; // Classic returns an empty object for some reason, so we return one too
  }

  @ApiOperation({
    summary: 'Get the translated permissions for an app',
  })
  @Get('/v2/apps/:appId/permissions/translated')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiTags(EAPITags.Apps)
  async getTranslatedPermissions(
    @Param('appId', new ParseIntPipe()) appId: number,
    @Query('permissions') permissionsParam: string | undefined,
    @ExtractOrgEnv() orgEnv: OrgEnv,
    @Headers('accept-language') acceptedLanguage?: string,
    @Headers('CloudFront-Viewer-Country') countryHeader?: string,
  ) {
    const countryCode = countryHeader ? countryHeader.toUpperCase() : 'ZZ';
    const permissions = permissionsParam?.split(',') ?? [];

    return await this.appService.getTranslatedPermissionsForApp(
      appId,
      orgEnv,
      countryCode,
      acceptedLanguage,
      permissions,
    );
  }

  @ApiOperation({
    summary: 'Activates app for a user',
  })
  @Post('/v2/apps/:appId/users/:userId/activate')
  @UseGuards(OauthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiCreatedResponse()
  @ApiTags(EAPITags.AppsUsers)
  @Policies({
    useClass: And,
    inject: [new HasScope(EOAuthScope.APP, EOAuthScope.USER), new IsOwnApp()],
  })
  async activeUserToAnotherApp(
    @Param('appId', new ParseIntPipe()) appId: number,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() body: AppsUsersActivateInputDTO,
    @ExtractOrgEnv() orgEnv: OrgEnv,
  ) {
    const isUserActivatedForApp = await this.userService.userActivatedForApp(userId, appId, orgEnv.id);
    if (isUserActivatedForApp) {
      throw new ConflictException('App already activated');
    }

    const { user, activationPermissions } = await this.userService.activateUserToApp(
      userId,
      appId,
      { permissions: body.permissions },
      orgEnv.id,
    );

    return {
      id: user.id,
      permissions: activationPermissions,
    };
  }

  @ApiOperation({
    summary: 'Gets app configuration for Niantic SSO Front end',
  })
  @Get(['/v2/apps/config', '/v1/apps/config'])
  @HttpCode(HttpStatus.OK)
  @ApiTags(EAPITags.Apps)
  @Public()
  async getAppConfig(@Query() { oauthClientId }: AppConfigParams, @ExtractOrgEnv() orgEnv: OrgEnv) {
    return await this.appService.getAppConfig(oauthClientId, orgEnv.id);
  }
}

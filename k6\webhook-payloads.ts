import {
    WebhookPayload,
    SettingsUserGraduatedPayloadDTO,
    SettingsEffectiveValuesChangedPayloadDTO,
    FamiliesUserRemovedFromFamilyDTO,
    FamiliesUserAddedToFamilyDTO,
    FamiliesGuardianRequestExpiredPayloadDto,
    FamiliesGroupDeletedPayloadDto
} from './webhook-endpoint-types';
import { ESettingConsentType } from '@superawesome/freekws-settings-common';

const optOutDefinition = {
    ageBracket: {
        consentType: "opt-out",
    },
};

const optInDefinition = {
    ageBracket: {
        consentType: "opt-in-verified",
    },
};

const commonWebhookData = {
    time: Date.now(),
    orgId: "9d14b0c5-fa1b-4590-8def-3f881ee021b7",
    productId: "48de3e54-33a8-499c-955a-4fd5070e79ab",
};

export const childAccountGraduatedPayload: WebhookPayload<SettingsUserGraduatedPayloadDTO> = {
    ...commonWebhookData,
    name: "settings:user-graduated",
    payload: {
        userId: '1234567'
    }
};

export const settingsEffectiveValuesChangedPayload: WebhookPayload<SettingsEffectiveValuesChangedPayloadDTO> = {
    ...commonWebhookData,
    name: "settings:effective-values-changed",
    payload: {
        userId: '2345678',
        settings: [
            {
                namespace: 'default',
                settingName: 'display-name',
                effectiveValue: true,
                definition: optOutDefinition,
            },
            {
                namespace: 'Stranger',
                settingName: 'things',
                effectiveValue: true,
                parentLimitedUpdatedAt: new Date().toISOString(),
                definition: optOutDefinition,
            },
            {
                namespace: 'default',
                settingName: 'complicated',
                effectiveValue: false,
                definition: optInDefinition,
            },
        ]
    }
};

export const userRemovedFromFamilyPayload: WebhookPayload<FamiliesUserRemovedFromFamilyDTO> = {
    ...commonWebhookData,
    name: "families:user-removed-from-family",
    payload: {
        userId: '3456789'
    }
};

export const userAddedToFamilyPayload: WebhookPayload<FamiliesUserAddedToFamilyDTO> = {
    ...commonWebhookData,
    name: "families:user-added-to-family",
    payload: {
        userId: '4567890'
    }
};

export const guardianRequestExpiredPayload: WebhookPayload<FamiliesGuardianRequestExpiredPayloadDto> = {
    ...commonWebhookData,
    name: "families:guardian-request-expired",
    payload: {
        userId: '5678901'
    }
};

export const familiesGroupDeletedPayload: WebhookPayload<FamiliesGroupDeletedPayloadDto> = {
    ...commonWebhookData,
    name: "families:family-group-deleted",
    payload: {
        members: [
            {
                userId: '6789012',
                role: 'MANAGER'
            },
            {
                userId: '7890123',
                role: 'SUPERVISED'
            },
            {
                userId: '8901234',
                role: 'SUPERVISED'
            }
        ]
    }
};

export const webhookPayloads = {
    childAccountGraduated: childAccountGraduatedPayload,
    settingsEffectiveValuesChanged: settingsEffectiveValuesChangedPayload,
    userRemovedFromFamily: userRemovedFromFamilyPayload,
    userAddedToFamily: userAddedToFamilyPayload,
    guardianRequestExpired: guardianRequestExpiredPayload,
    familiesGroupDeleted: familiesGroupDeletedPayload
}; 
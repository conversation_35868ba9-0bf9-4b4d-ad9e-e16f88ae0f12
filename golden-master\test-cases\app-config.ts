import { EndpointConfig, WorkflowConfig } from '../config';

export const appConfigTests: (EndpointConfig | WorkflowConfig)[] = [
    {
        name: 'Age gate',
        path: '/v1/countries/child-age?country=GB&dob=2015-12-12',
        method: 'GET',
    },
    {
        name: 'Age gate - lower case country',
        path: '/v1/countries/child-age?country=gb&dob=2015-12-12',
        method: 'GET',
        bodyFieldConditions: {
            country: 'present',
        },
    },
    {
        name: 'Age gate - alias',
        path: '/v1/countries/childage?country=GB&dob=2015-12-12',
        method: 'GET',
    },
    {
        name: 'Get Translation Permissions - All Permissions',
        path: '/v2/apps/:appId/permissions/translated',
        method: 'GET',
        sortKey: "name",
    },
    {
        name: 'Get Translation Permissions - One Permission',
        path: '/v2/apps/:appId/permissions/translated?permissions=complicated',
        method: 'GET',
        sortKey: "name",
    },
    {
        name: 'Get Translation Permissions - Two Permissions',
        path: '/v2/apps/:appId/permissions/translated?permissions=complicated,privacy-policy',
        method: 'GET',
        sortKey: "name",
    },
    {
        name: 'Get Config',
        path: `/v2/apps/config?oauthClientId=javi-test`,
        method: 'GET',
        sortKey: "name",
        bodyFieldConditions: {
            "app.parentEmailRequired": 'present',
            "identityName": 'present',
        }
    },
    {
        name: 'Get Config Aliased v1',
        path: `/v1/apps/config?oauthClientId=javi-test`,
        method: 'GET',
        sortKey: "name",
        bodyFieldConditions: {
            "app.parentEmailRequired": 'present',
            "identityName": 'present',
        }
    }
]; 
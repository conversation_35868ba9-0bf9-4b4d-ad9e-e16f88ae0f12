import { EndpointConfig, WorkflowConfig } from '../config';


export const authenticationTests: (EndpointConfig | WorkflowConfig)[] = [
    {
        // It is slightly possible for this to fail if JW<PERSON>'s get out of sync, or a new one is created
        // Just as the requests fire off
        name: "Get latest 20 JWKs",
        path: '/v1/jwks',
        method: 'GET',
    },
    {
        name: 'OAuth Token - client_credentials flow (javiTest)',
        path: '/oauth/token',
        method: 'POST',
        payload: {
            client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
            client_secret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
            grant_type: 'client_credentials',
        },
        contentType: 'application/x-www-form-urlencoded',
        skipAuth: true,
        bodyFieldConditions: {
            access_token: 'present',
            token_type: 'present',
            expires_in: 'present',
            refresh_token: 'present',
        },
        skipHeaders: ['access-control-expose-headers', 'x-content-type-options', 'x-ratelimit-window'],
    },
    {
        name: 'OAuth Token - password flow',
        path: '/oauth/token',
        method: 'POST',
        payload: {
            client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
            client_secret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
            grant_type: 'password',
            username: 'OAuthTestUser',
            password: 'OAuthTestUserPassword',
            scope: 'app',
        },
        contentType: 'application/x-www-form-urlencoded',
        skipAuth: true,
        bodyFieldConditions: {
            access_token: 'present',
            token_type: 'present',
            expires_in: 'present',
            refresh_token: 'present',
        },
        skipHeaders: ['access-control-expose-headers', 'x-content-type-options', 'x-ratelimit-window'],
    },
    {
        name: 'OAuth Token - refresh_token flow',
        workflow: [
            () => {
                return {
                    name: 'Get initial token for refresh',
                    path: '/oauth/token',
                    method: 'POST',
                    payload: {
                        client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
                        client_secret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
                        grant_type: 'client_credentials',
                        scope: 'app',
                    },
                    contentType: 'application/x-www-form-urlencoded',
                    skipAuth: true,
                    bodyFieldConditions: {
                        access_token: 'present',
                        refresh_token: 'present',
                    },
                    skipHeaders: ['access-control-expose-headers', 'x-content-type-options', 'x-ratelimit-window'],
                };
            },
            (response) => {
                return {
                    name: 'Use refresh token to get new token',
                    path: '/oauth/token',
                    method: 'POST',
                    payload: {
                        client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
                        client_secret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
                        grant_type: 'refresh_token',
                        refresh_token: response.resBody.refresh_token,
                    },
                    headers: {
                        'x-forwarded-host': 'kwsapi.v1staging.kidswebservices.com',
                    },
                    contentType: 'application/x-www-form-urlencoded',
                    skipAuth: true,
                    bodyFieldConditions: {
                        access_token: 'present',
                        token_type: 'present',
                        expires_in: 'present',
                        refresh_token: 'present',
                    },
                    skipHeaders: ['access-control-expose-headers', 'x-content-type-options', 'x-ratelimit-window'],
                };
            },
        ],
    },
    {
        name: 'OAuth Token - authorization_code flow',
        workflow: [
            () => {
                return {
                    name: 'Get authorization code',
                    path: '/oauth/authorise',
                    method: 'POST',
                    payload: {
                        client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
                        redirect_uri: 'https://jellymarr.io/',
                        response_type: 'code',
                        state: 'test-state',
                    },
                    useAuth: 'javiTest',
                    user: {
                        username: 'OAuthTestUser',
                        password: 'OAuthTestUserPassword',
                    },
                    workflowState: {
                        authorization_code: 'body.code',
                    },
                    bodyFieldConditions: {
                        code: 'present',
                    },
                };
            },
            (response) => {
                return {
                    name: 'Exchange authorization code for tokens',
                    path: '/oauth/token',
                    method: 'POST',
                    payload: {
                        client_id: process.env.WRAPPER_CLIENT_ID || 'not-provided',
                        client_secret: process.env.WRAPPER_CLIENT_SECRET || 'not-provided',
                        grant_type: 'authorization_code',
                        code: response.resBody.code,
                        redirect_uri: 'https://jellymarr.io/',
                    },
                    headers: {
                        'x-forwarded-host': 'kwsapi.v1staging.kidswebservices.com',
                    },
                    contentType: 'application/x-www-form-urlencoded',
                    skipAuth: true,
                    bodyFieldConditions: {
                        access_token: 'present',
                        token_type: 'present',
                        expires_in: 'present',
                        refresh_token: 'present',
                    },
                    skipHeaders: ['access-control-expose-headers', 'x-content-type-options', 'x-ratelimit-window'],
                };
            },
        ],
    }
]; 
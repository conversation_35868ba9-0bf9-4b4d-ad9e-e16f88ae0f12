# Environment variables for Sequential Scenario tests
# ----------------------------------------------------
# The base URL of the application under test
BASE_URL=http://localhost:7001

# OAuth credentials for the main app used in tests
CLIENT_ID=your-client-id
CLIENT_SECRET=your-client-secret

# Specific credentials for the "Dom Craft" test application
DOM_CRAFT_CLIENT_ID=dom-craft-client-id
DOM_CRAFT_SECRET=dom-craft-client-secret

# Optional: Load parameters
TARGET_QPS=100
TEST_DURATION=60
COOLDOWN_DURATION=60


# Environment variables for Webhook tests
# ---------------------------------------
# The base URL of the application under test (can be the same as above)
# BASE_URL=http://localhost:7001

# Optional: Specify a single webhook to test.
# Valid options: child-graduated, settings-changed, user-removed, user-added, guardian-expired, family-deleted
# If commented out, all webhooks will be tested in a round-robin fashion.
WEBHOOK_TYPE=child-graduated
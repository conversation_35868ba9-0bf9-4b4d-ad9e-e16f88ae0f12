import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserUsernameOrgEnvIdIndex1741700000000 implements MigrationInterface {
    name = 'UpdateUserUsernameOrgEnvIdIndex1741700000000';
    transaction = false;

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX CONCURRENTLY IF EXISTS "user_username_orgEnvId_idx"
        `);

        await queryRunner.query(`
            CREATE UNIQUE INDEX CONCURRENTLY user_username_orgEnvId_idx
                ON "user"
                    (
                     "orgEnvId",
                     LOWER(username) text_pattern_ops
                        )
                WHERE "deletedAt" IS NULL
                    AND username IS NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX CONCURRENTLY IF EXISTS "user_username_orgEnvId_idx"
        `);

        await queryRunner.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS "user_username_orgEnvId_idx"
                ON "user" (LOWER("username"), "orgEnvId")
                WHERE "deletedAt" IS NULL
        `);
    }
}

import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable, paramsExtractor } from '@superawesome/freekws-cache-decorator';
import {
  AppUserParams,
  GetUserResponseDTO,
  Permissions,
  UserCreateDTO,
  UserCreateResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { formatDate } from 'date-fns';
import { Span } from 'nestjs-ddtrace';
import { Repository } from 'typeorm';

import { AppTranslation } from './app-translation.entity';
import { App } from './app.entity';
import {
  IAppInfo,
  IAppOauthClient,
  IAppOauthClientCredentials,
  TRequestUserPermissions,
  TUpdateParentEmailParams,
} from './types';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { AnalyticService } from '../common/services/analytic/analytic.service';
import { DEFAULT_T_AND_C_PERMISSION_NAME, SettingsService } from '../common/services/settings/settings.service';
import {
  SettingsServiceConsentNotRequestedError,
  SettingsServiceParentMissingError,
  SettingsServiceParentNotInFamilyError,
} from '../common/services/settings/types';
import { User } from '../user/user.entity';
import { UserService } from '../user/user.service';

export const NIANTIC_ORG_ENV_ID = 'a0ebae99-8a3d-42c4-98db-a5220c6a56b8';

@Injectable()
@Span()
export class AppService {
  constructor(
    @InjectRepository(App) private readonly appRepo: Repository<App>,
    @InjectRepository(AppTranslation) private readonly appTranslationsRepo: Repository<AppTranslation>,
    @Inject(forwardRef(() => UserService)) private readonly userService: UserService,
    private readonly ageGateService: AgeGateService,
    private readonly analyticService: AnalyticService,
    private readonly settingsService: SettingsService,
  ) {}

  async getAppOauthClient(
    { clientId, secret }: IAppOauthClientCredentials,
    orgEnvId: string,
  ): Promise<IAppOauthClient> {
    const app = await this.appRepo.findOneOrFail({
      where: [
        {
          oauthClientId: clientId,
          apiKey: secret,
          orgEnvId,
        },
        {
          oauthClientId: clientId,
          mobileApiKey: secret,
          orgEnvId,
        },
      ],
    });

    return {
      appId: app.id,
      clientId: app.oauthClientId,
      redirectUri: app.oauthCallback,
      orgEnvId: app.orgEnvId,
    };
  }

  @Cacheable(300, 'getAppInfo', paramsExtractor(0, 1))
  async getAppInfo(orgEnvId: string, id: number) {
    const app = await this.appRepo.findOneOrFail({
      relations: { orgEnv: true },
      where: { id: id ?? -1, orgEnvId },
    });
    return this.makeAppInfo(app);
  }

  @Cacheable(300, 'getAppInfoBy', paramsExtractor(0, 1))
  async getAppInfoBy(appId: number, orgEnvId: string) {
    const app = await this.appRepo.findOneOrFail({
      relations: { orgEnv: true, oauthCallbackUrls: true },
      where: { id: appId, orgEnvId },
    });
    return this.makeAppInfo(app);
  }

  makeAppInfo(app: App) {
    return {
      id: app.id,
      orgId: app.orgEnv.orgId,
      orgEnvId: app.orgEnv.id,
      productId: app.productId,
      credentials: { clientId: app.orgEnv.clientId, secret: app.orgEnv.clientSecret },
      app,
    };
  }

  async getAppInfoByUser(orgEnvId: string, userId: number) {
    const app = await this.appRepo.findOneOrFail({
      relations: { orgEnv: true },
      where: {
        orgEnvId,
        activations: {
          user: { id: userId },
        },
      },
    });

    return {
      productId: app.productId,
      credentials: { clientId: app.orgEnv.clientId, secret: app.orgEnv.clientSecret },
    };
  }

  async appByOauthClientId(orgEnvId: string, oauthClientId: string | undefined): Promise<App | null> {
    return await this.appRepo.findOneBy({
      orgEnvId,
      oauthClientId,
    });
  }

  async registerUser(orgEnvId: string, userCreateData: UserCreateDTO, appId: number): Promise<UserCreateResponseDTO> {
    const appInfo = await this.getAppInfo(orgEnvId, appId);

    const ageGatePromise = this.ageGateService.getConsentAgeForCountry(
      {
        location: userCreateData.country,
        dob: userCreateData.dateOfBirth,
      },
      appInfo.credentials,
    );
    const userCreatePromise = this.userService.create({
      orgEnvId,
      language: userCreateData.language,
      dateOfBirth: userCreateData.dateOfBirth,
      signUpCountry: userCreateData.country,
      appId,
    });

    const [ageGateInfo, user] = await Promise.all([ageGatePromise, userCreatePromise]);

    let createdPermissions: Permissions = {};

    if (userCreateData.permissions?.length) {
      if (ageGateInfo.underAgeOfDigitalConsent) {
        try {
          await this.settingsService.sendConsentEmail(
            {
              userId: user.id,
              productId: appInfo.productId,
              dob: userCreateData.dateOfBirth,
              parentEmail: userCreateData.parentEmail,
              language: userCreateData.language,
              location: userCreateData.country,
              permissions: userCreateData.permissions,
            },
            appInfo.credentials,
          );
          const settings = await this.settingsService.getUserSettings(
            {
              userId: user.id,
              productId: appInfo.productId,
            },
            appInfo.credentials,
          );
          createdPermissions = SettingsService.transformSettingsToPermissions(settings, userCreateData.permissions);
        } catch (error) {
          if (error instanceof SettingsServiceParentMissingError) {
            throw new BadRequestException('parentEmail required');
          }
          throw error;
        }
      } else {
        createdPermissions = Object.fromEntries(userCreateData.permissions.map((cur) => [`${cur}`, true]));
      }
    }

    this.analyticService.signupSuccess(userCreateData.country, user.id, undefined, ageGateInfo.userAge, {
      language: user.language,
    });

    return {
      id: user.id,
      uuid: user.uuid,
      isMinor: ageGateInfo.underAgeOfDigitalConsent ?? true,
      permissions: createdPermissions,
    };
  }

  async requestPermissionsForUser(
    orgEnvId: string,
    {
      userId,
      appId,
      permissions,
      parentEmail,
      dateOfBirth,
    }: TRequestUserPermissions & {
      permissions: string[];
    },
  ) {
    const appInfo = await this.getAppInfo(orgEnvId, appId);
    const userData = await this.userService.getById(orgEnvId, userId);
    if (!userData) {
      throw new NotFoundException(`User ID ${userId} not found`);
    }
    const user = { ...userData, dateOfBirth: userData.dateOfBirth && formatDate(userData.dateOfBirth, 'yyyy-MM-dd') };

    await this.verifyAgeGateConsent(user, dateOfBirth, appInfo);

    await this.updateDateOfBirthIfMissing(user, dateOfBirth, orgEnvId, userId);

    const userHasActivation = await this.userHasActivation(orgEnvId, userId, appId);
    let userSettings: UserSettingValueDTO[] = [];
    if (userHasActivation) {
      userSettings = await this.settingsService.getUserSettings(
        {
          userId: user.id,
          productId: appInfo.productId,
        },
        appInfo.credentials,
      );
    }

    let permissionsToRequest = permissions;
    if (appInfo.app.termsAndConditionsRequired && !this.hasAcceptedTermsAndConditions(userSettings)) {
      permissionsToRequest = [DEFAULT_T_AND_C_PERMISSION_NAME, ...permissions];
    }

    try {
      await this.settingsService.sendConsentEmail(
        {
          userId: user.id,
          productId: appInfo.productId,
          dob: user.dateOfBirth || dateOfBirth,
          parentEmail,
          language: user.language,
          location: user.signUpCountry,
          permissions: permissionsToRequest,
        },
        appInfo.credentials,
      );
    } catch (error) {
      if (error instanceof SettingsServiceParentMissingError) {
        throw new BadRequestException('No parent email.');
      }
      throw error;
    }

    return { user, userSettings };
  }

  private async updateDateOfBirthIfMissing(
    user: {
      dateOfBirth: string | undefined;
    },
    dateOfBirth: string | undefined,
    orgEnvId: string,
    userId: number,
  ) {
    if (!user.dateOfBirth && dateOfBirth) {
      await this.userService.updateWithMissingValues(orgEnvId, userId, {
        dateOfBirth: new Date(dateOfBirth),
      });
    }
  }

  private getResultantPermissions(
    userSettingsWithoutTermsAndConditions: UserSettingValueDTO[],
    permissions: string[],
    user: Pick<User, 'language'>,
  ) {
    return SettingsService.transformSettingsToGroupedPermissions(
      userSettingsWithoutTermsAndConditions,
      permissions,
      user.language,
    );
  }

  private async verifyAgeGateConsent(
    user: Pick<User, 'signUpCountry'> & {
      dateOfBirth: string | undefined;
    },
    dateOfBirth: string | undefined,
    appInfo: IAppInfo,
  ) {
    if (user.dateOfBirth || dateOfBirth) {
      const ageGateInfo = await this.ageGateService.getConsentAgeForCountry(
        {
          location: user.signUpCountry,
          dob: dateOfBirth && user.dateOfBirth !== dateOfBirth ? dateOfBirth : user.dateOfBirth,
        },
        appInfo.credentials,
      );

      if (!ageGateInfo.underAgeOfDigitalConsent) {
        throw new ForbiddenException('operation requested is not permitted for graduated users');
      }
    }
  }

  async updateParentEmail(orgEnvId: string, { appId, userId, parentEmail }: TUpdateParentEmailParams) {
    const appInfo = await this.getAppInfo(orgEnvId, appId);
    const user = await this.userService.getById(orgEnvId, userId);
    if (!user) {
      throw new BadRequestException(`User ID ${userId} not found`);
    }

    try {
      await this.settingsService.resendConsentEmail(
        {
          userId: user.id,
          productId: appInfo.productId,
          parentEmail,
          dob: user.dateOfBirth ? formatDate(user.dateOfBirth, 'yyyy-MM-dd') : undefined,
          language: user.language,
          location: user.signUpCountry,
        },
        appInfo.credentials,
      );
    } catch (error) {
      if (error instanceof SettingsServiceParentNotInFamilyError) {
        throw new ConflictException({
          customResponseBody: true,
          codeMeaning: 'parentAlreadyVerified',
          errorMessage: 'parent already verified',
        });
      }
      if (error instanceof SettingsServiceConsentNotRequestedError) {
        throw new ConflictException('cannot update parent email of graduated user');
      }
      throw error;
    }
  }

  async getUser(orgEnvId: string, { appId, userId }: AppUserParams): Promise<GetUserResponseDTO> {
    const [appInfo, user] = await Promise.all([
      this.getAppInfo(orgEnvId, appId),
      this.userService.getById(orgEnvId, userId),
    ]);

    if (!user) {
      throw new BadRequestException(`User ID ${userId} not found`);
    }

    const ageGatePromise = this.ageGateService.getConsentAgeForCountry(
      {
        location: user.signUpCountry,
        dob: user.dateOfBirth ? formatDate(user.dateOfBirth, 'yyyy-MM-dd') : undefined,
      },
      appInfo.credentials,
    );
    const settingsPromise = this.settingsService.getUserSettings(
      { userId, productId: appInfo.productId },
      appInfo.credentials,
    );
    const parentEmailPromise = this.userService.getParentEmail(user.id);

    const [ageGateInfo, userSettings, parentEmail] = await Promise.all([
      ageGatePromise,
      settingsPromise,
      parentEmailPromise,
    ]);

    const userSettingsWithoutTermsAndConditions = SettingsService.removeTermsAndConditionsSetting(userSettings);
    const permissions = SettingsService.transformSettingsToAllPermissions(userSettingsWithoutTermsAndConditions);

    const parentDetails = {
      oauthProvider: null,
      usedVerificationMethodName: 'KWS',
    };

    if (!parentEmail) {
      return {
        id: user.id,
        language: user.language,
        createdAt: user.createdAt,
        dateOfBirth: user.dateOfBirth,
        username: null,
        displayName: null,
        activationCreatedAt: user.createdAt,
        permissions,
        isMinor: ageGateInfo.underAgeOfDigitalConsent ?? true,
        consentAgeForCountry: ageGateInfo.consentAge,
        parentDetails,
      };
    }

    return {
      id: user.id,
      language: user.language,
      createdAt: user.createdAt,
      dateOfBirth: user.dateOfBirth,
      username: null,
      displayName: null,
      activationCreatedAt: user.createdAt,
      permissions,
      isMinor: ageGateInfo.underAgeOfDigitalConsent ?? true,
      consentAgeForCountry: ageGateInfo.consentAge,
      parentEmail,
      parentState: this.userService.getParentStateAllFalse(),
      parentDetails,
    };
  }

  async userHasActivation(orgEnvId: string, userId: number, appId: number): Promise<boolean> {
    const activation = await this.userService.getUserActivation(orgEnvId, userId, appId);
    return !!activation;
  }

  async reviewPermissions(userId: number, appId: number, orgEnvId: string): Promise<void> {
    const appInfo = await this.getAppInfo(orgEnvId, appId);
    const user = await this.userService.getById(orgEnvId, userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const userSettings = await this.settingsService.getUserSettings(
      {
        userId,
        productId: appInfo.productId,
      },
      appInfo.credentials,
    );

    const previouslyRequestedSettings = userSettings.filter((setting) => setting.consentRequestedAt !== undefined);

    if (previouslyRequestedSettings.length === 0) {
      return; // Nothing to review if no settings have been requested before
    }

    const parentEmail = await this.userService.getParentEmail(userId);
    if (!parentEmail) {
      throw new BadRequestException('No parent email found associated with the user.');
    }

    try {
      await this.settingsService.sendConsentEmail(
        {
          userId,
          productId: appInfo.productId,
          settings: previouslyRequestedSettings,
          parentEmail,
          language: user.language,
          dob: user.dateOfBirth ? formatDate(user.dateOfBirth, 'yyyy-MM-dd') : undefined,
        },
        appInfo.credentials,
      );
    } catch (error) {
      if (error instanceof SettingsServiceParentMissingError) {
        throw new BadRequestException('No parent email.');
      }
      throw error;
    }
  }

  private hasAcceptedTermsAndConditions(userSettings: UserSettingValueDTO[]) {
    return userSettings.some(
      (userSetting) => SettingsService.isTermsAndConditionsSetting(userSetting) && userSetting.effectiveValue === true,
    );
  }

  async getTranslatedPermissionsForApp(
    appId: number,
    orgEnv: { id: string; orgId: string },
    countryCode: string,
    acceptedLanguage: string | undefined,
    permissions: string[],
  ) {
    const { app } = await this.getAppInfoBy(appId, orgEnv.id);

    const settingsConfigurations = await this.settingsService.getProductSettingsDefinition({
      productId: app.productId,
      productEnvId: app.productEnvId,
      orgId: orgEnv.orgId,
      orgEnvId: orgEnv.id,
    });

    const settingDefinitions = SettingsService.removeTermsAndConditionsSetting(
      settingsConfigurations.flatMap((settingsConfiguration) => {
        return settingsConfiguration.settings.map((setting) => {
          return {
            namespace: settingsConfiguration.namespace,
            settingName: setting.settingName,
            label: setting.label,
            userNotice: setting.userNotice,
            parentNotice: setting.parentNotice,
          };
        });
      }),
    );

    const translations = settingDefinitions.map((settingConfig) => {
      return {
        name: SettingsService.settingToPermissionName(settingConfig),
        displayName: this.getTranslation(settingConfig.label, acceptedLanguage),
        childFacingDescription: this.getTranslation(settingConfig.userNotice, acceptedLanguage),
        privacyNotice: this.getTranslation(settingConfig.parentNotice, acceptedLanguage),
      };
    });

    if (permissions.length === 0) {
      return translations;
    }

    return translations.filter((translation) => permissions.includes(translation.name));
  }

  private getTranslation(translations: Record<string, string>, language?: string) {
    const extractTranslation = () => {
      const languagesKeys = Object.keys(translations);
      const languagesAvailable = languagesKeys.length;
      if (languagesAvailable === 1) {
        return translations[languagesKeys[0]];
      }

      if (!language) {
        return translations['en'];
      }

      return translations[language.toLowerCase()] ?? translations['en'];
    };

    const translation = extractTranslation();
    const translationSplit = translation.split('\n\n');
    if (translationSplit && translationSplit?.length > 1) {
      return translationSplit.slice(-1).join('\n\n');
    } else {
      return translation;
    }
  }

  async getAppConfig(oauthClientId: string, orgEnvId: string) {
    const app = await this.appRepo.findOneOrFail({
      where: { oauthClientId, orgEnvId },
      relations: { oauthCallbackUrls: true },
    });
    const translations = await this.appTranslationsRepo.find({
      where: {
        appId: app.id,
        orgEnvId,
      },
    });

    const translatedKeys = [
      'logoUrl',
      'iconUrl',
      'description',
      'headerLogoUrl',
      'faviconUrl',
      'splashScreenLogoUrl',
      'splashScreenBgImageUrl',
      'mainContainerBgImageUrl',
      'mainContainerBgImageFill',
      'termsAndConditionsUrl',
      'privacyPolicyUrl',
      'brandingEmailHeaderBgImg',
      'emailFooterCopy',
    ] satisfies (keyof AppTranslation)[];

    const languagesAvailable = translations.map((translation) => translation.language);

    const translationsByLanguage = translatedKeys.reduce((acc, configProperty) => {
      for (const language of languagesAvailable) {
        const translation = translations.find((translation) => translation.language === language);
        if (!translation) {
          throw new Error(`Translation not found for language ${language}`);
        }
        if (!acc[configProperty]) {
          acc[configProperty] = {};
        }
        if (configProperty === 'mainContainerBgImageFill') {
          // This is a boolean field in classic and in the wrapper it is a string?
          acc[configProperty][language] = translation[configProperty] === 'true';
          continue;
        }
        acc[configProperty][language] = translation[configProperty] ?? null;
      }
      return acc;
    }, {} as Record<string, Record<string, string | null | boolean>>);

    // Creates object with null values for all languages
    const createNullLanguageSet = () => {
      return languagesAvailable.reduce((acc, language) => {
        acc[language] = null;
        return acc;
      }, {} as Record<string, null>);
    };

    // We don't store these in the wrapper, and they're always null for Niantic
    translationsByLanguage['ssoTextSignupPage'] = createNullLanguageSet();
    translationsByLanguage['ssoTextParentEmailPage'] = createNullLanguageSet();
    translationsByLanguage['ssoTextSuccessPage'] = createNullLanguageSet();

    return {
      app: {
        ...translationsByLanguage,
        id: app.id,
        name: app.name,
        oauthClientId: app.oauthClientId,
        termsAndConditionsRequired: app.termsAndConditionsRequired,
        mainContainerBgColour: app.oauthClientId === 'pokemon-go' ? '#1F5686' : null,
        oauthImplicitEnabled: false, // Most set to false except 5, these seem like tests apps
        parentEmailRequired: true, // All values true in classic for Niantic, but one "StarterKitBolt", only has 35 activations
        areAnonAccountsEnabled: false, // All values false in classic for Niantic, but one "StarterKitBolt", only has 35 activations
        verificationRequired: false, // All values false in classic for Niantic
        displayNameRequired: true, // All values true in classic for Niantic
        oauthCallbackUrls: this.getCallbackUrls(app),
        defaultOauthCallbackUrl: null, // Niantic has no default callback url set, so this is always null from classic
      },
      languages: languagesAvailable,
      identityName: orgEnvId === NIANTIC_ORG_ENV_ID ? 'Niantic Kids' : 'Unknown',
    };
  }

  private getCallbackUrls(app: App) {
    return app.oauthCallbackUrls.map((callbackUrl) => callbackUrl.url);
  }
}

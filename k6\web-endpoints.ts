import { addAuth<PERSON>eader, validateResponse, generateKwsSignature } from './utils';
import http from 'k6/http';
import {
    FamiliesGroupDeletedPayloadDto,
    FamiliesGuardianRequestExpiredPayloadDto,
    FamiliesUserAddedToFamilyDTO,
    FamiliesUserRemovedFromFamilyDTO,
    SettingsEffectiveValuesChangedPayloadDTO,
    SettingsUserGraduatedPayloadDTO,
    WebhookPayload
} from './endpoint-types';

export const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
export const DEFAULT_HEADERS = {
    'x-forwarded-host': 'test.com',
    'Content-Type': 'application/json'
};

// Webhook endpoints
export function webhookChildAccountGraduated(body: WebhookPayload<SettingsUserGraduatedPayloadDTO>) {
    const signature = generateKwsSignature(body, "secretsecret");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/child-account-graduated`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookSettingsEffectiveValuesChanged(body: WebhookPayload<SettingsEffectiveValuesChangedPayloadDTO>) {
    const signature = generateKwsSignature(body, "secretsecret6");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/settings-effective-values-changed`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookUserRemovedFromFamily(body: WebhookPayload<FamiliesUserRemovedFromFamilyDTO>) {
    const signature = generateKwsSignature(body, "secretsecret4");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-removed-from-family`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookUserAddedToFamily(body: WebhookPayload<FamiliesUserAddedToFamilyDTO>) {
    const signature = generateKwsSignature(body, "secretsecret3");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/user-added-to-family`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookGuardianRequestExpired(body: WebhookPayload<FamiliesGuardianRequestExpiredPayloadDto>) {
    const signature = generateKwsSignature(body, "secretsecret7");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/guardian-request-expired`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

export function webhookFamiliesGroupDeleted(body: WebhookPayload<FamiliesGroupDeletedPayloadDto>) {
    const signature = generateKwsSignature(body, "secretsecret");

    const headers = {
        ...DEFAULT_HEADERS,
        'x-kws-signature': signature,
    };

    const response = http.post(
        `${BASE_URL}/v1/webhooks/families-group-deleted`,
        JSON.stringify(body),
        { headers }
    );

    validateResponse(response, { expectedStatus: 204, maxDuration: 500 });

    return response;
}

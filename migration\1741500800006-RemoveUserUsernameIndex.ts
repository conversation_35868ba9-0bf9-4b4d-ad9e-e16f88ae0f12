import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveUserUsernameIndex1741500800006 implements MigrationInterface {
    name = 'RemoveUserUsernameIndex1741500800006';
    transaction = false

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX CONCURRENTLY "public"."user_username_orgEnvId"
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE UNIQUE INDEX CONCURRENTLY "user_username_orgEnvId" ON "user" (LOWER("username"), "orgEnvId")
        `);
    }
}

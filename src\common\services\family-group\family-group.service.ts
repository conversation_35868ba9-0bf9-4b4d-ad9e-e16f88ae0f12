import { Inject, Injectable } from '@nestjs/common';
import { KeycloakService } from '@superawesome/freekws-auth-library';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { FAMILY_SERVICE_CLIENT_INJECT_KEY, familyServicePlugin } from '@superawesome/freekws-family-service-common';

import { ClientKeycloakService } from '../keycloak/client-keycloak.service';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

@Injectable()
export class FamilyGroupService {
  constructor(
    private readonly clientKeycloakService: ClientKeycloakService,
    @Inject(FAMILY_SERVICE_CLIENT_INJECT_KEY)
    private readonly client: NestJsClient<typeof familyServicePlugin>,
    @Inject(KEYCLOAK_PROVIDER)
    private readonly keycloakClient: KeycloakService,
  ) {}

  async getGuardianLinks(userId: number) {
    const axoisResponse = await this.client.getModule('internalAdminFamilyGroup').findGuardianLinks(
      {
        query: { userId: `${userId}` },
      },
      {
        headers: {
          Authorization: `Bearer ${await this.keycloakClient.getUpToDateServiceAccessToken()}`,
        },
      },
    );

    return axoisResponse.data.response;
  }

  async getGuardianLinksFromEmail(email: string) {
    const axoisResponse = await this.client.getModule('internalAdminFamilyGroup').findGuardianLinks(
      {
        query: { email: email },
      },
      {
        headers: {
          Authorization: `Bearer ${await this.keycloakClient.getUpToDateServiceAccessToken()}`,
        },
      },
    );

    return axoisResponse.data.response;
  }
}

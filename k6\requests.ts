import http from 'k6/http';
import { check } from 'k6';
import {
    TARGET_TIME_CREATION_REQUEST,
    TARGET_TIME_OTHER_REQUEST
} from './sequential-scenarios-test';

const BASE_URL = __ENV.BASE_URL || 'http://localhost:7001';

const FORWARDED_HOST = 'kwsapi.v1staging.kidswebservices.com';
const DEFAULT_HEADERS = {
    'x-forwarded-host': FORWARDED_HOST,
    'Content-Type': 'application/json'
};

function addAuthHeader(token: string) {
    return {
        ...DEFAULT_HEADERS,
        'Authorization': `Bearer ${token}`
    };
}

function generateRandomString(): string {
    return Math.random().toString(36).slice(2, 15);
}

function processPayload(payload: any): any {
    const payloadStr = JSON.stringify(payload);
    return JSON.parse(payloadStr.replace(/{random}/g, generateRandomString()));
}

function randomDob() {
    const start = new Date(2000, 0, 1).getTime();
    const end = new Date(2024, 11, 31).getTime();
    const randTs = Math.floor(Math.random() * (end - start + 1)) + start;
    const d = new Date(randTs);
    const yyyy = d.getUTCFullYear();
    const mm = String(d.getUTCMonth() + 1).padStart(2, '0');
    const dd = String(d.getUTCDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
}

const COUNTRY_CODES = ['CN', 'IN', 'US', 'ID', 'PK', 'BR', 'NG', 'BD', 'RU', 'MX', 'JP', 'ET', 'PH', 'EG', 'VN', 'CD', 'TR', 'IR', 'DE', 'TH',
];

function randomCountry() {
    return COUNTRY_CODES[Math.floor(Math.random() * COUNTRY_CODES.length)];
}

export function getOAuthToken(clientId: string, clientSecret: string): string | null {
    const payload = {
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'client_credentials',
        scope: 'app'
    };

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'x-forwarded-host': FORWARDED_HOST
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, {
        headers,
        tags: { url: '/oauth/token' }
    });

    if (response.status === 200) {
        return response.json('access_token') as string;
    }

    console.error(`Failed to get OAuth token: ${response.status} ${response.body}`);
    console.error(`OAuth response ${JSON.stringify(response, null, 2)}`);
    return null;
}

export function getOAuthUserToken(username: string, password: string) {
    const payload = {
        username,
        password,
        client_id: __ENV.CLIENT_ID,
        client_secret: __ENV.CLIENT_SECRET,
        grant_type: 'password',
        scope: 'user'
    };

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'x-forwarded-host': FORWARDED_HOST
    };

    const response = http.post(`${BASE_URL}/oauth/token`, payload, {
        headers,
        tags: { url: '/oauth/token' }
    });

    if (response.status === 200) {
        const accessToken = response.json('access_token');
        return accessToken as string;
    }

    console.error(`Failed to get OAuth token: ${response.status} ${response.body}`);
    return null;
}

const JAVI_TEST_APP_ID = '207800543';
const DOM_CRAFT_APP_ID = '325570412';
const TEST_USER_IDS = [
    "831076361", "960110049", "966969230", "980070205", "923805928"
    , "908605808"
    , "870241144"
    , "898570513"
    , "844678628"
];


function getRandomUserId(): string {
    return TEST_USER_IDS[Math.floor(Math.random() * TEST_USER_IDS.length)];
}

const TEST_PASSWORD = "test-password";


function createFreshUserV2(token: string) {
    const payload = processPayload({
        username: "load-test-{random}",
        password: TEST_PASSWORD,
        parentEmail: "load-test-{random}@epicgames.com",
        originAppId: parseInt(JAVI_TEST_APP_ID),
        token: "test-token",
        dateOfBirth: "2022-05-13"
    });

    const headers = {
        ...addAuthHeader(token),
        "x-kws-bypass-talon": "true"
    };

    const response = http.post(`${BASE_URL}/v2/users`, JSON.stringify(payload), {
        headers,
        tags: { url: '/v2/users' }
    });

    if (response.status === 201) {
        const user = response.json() as any;
        return { id: user.id, username: payload.username } as { id: number, username: string };
    }

    console.error(`Failed to create fresh user: ${response.status} ${response.body}`);
    return null;
}

function createFreshUserV2Apps(token: string) {
    const payload = processPayload({
        country: 'GM',
        language: 'en',
        dateOfBirth: '2020-10-10',
        parentEmail: 'load-test-fresh-user-{random}@epicgames.com',
        permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
    });

    const headers = addAuthHeader(token);
    const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users`, JSON.stringify(payload), {
        headers,
        tags: { url: '/v2/apps/:appId/users' }
    });

    if (response.status === 201) {
        const user = response.json() as any;
        return { id: user.id, username: payload.username } as { id: number, username: string };
    }

    console.error(`Failed to create fresh user: ${response.status} ${response.body}`);
    return null;
}

export const requests = {
    oauthTokenClientCredentials: () => {
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'client_credentials',
            scope: 'app'
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': FORWARDED_HOST
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, {
            headers,
            tags: { url: '/oauth/token' }
        });

        return check(response, {
            'oauth-client-credentials status is 200': (r) => r.status === 200,
            'oauth-client-credentials has access_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.access_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials has refresh_token': (r) => {
                try {
                    const body = JSON.parse(r.body as string);
                    return body.refresh_token !== undefined;
                } catch {
                    return false;
                }
            },
            'oauth-client-credentials response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    oauthTokenPassword: () => {
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'password',
            scope: 'user',
            username: "Penny",
            password: "BigBreakaway"
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': FORWARDED_HOST
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, {
            headers,
            tags: { url: '/oauth/token' }
        });

        return check(response, {
            'oauth-password status is 200 or 401': (r) => r.status === 200,
            'oauth-password response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
            'oauth-password has proper response': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined && body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                } else if (r.status === 401) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.error !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
        });
    },

    oauthTokenRefreshToken: () => {
        // If this test starts failing, the refresh token is no longer valid most likely
        const refreshToken = "0927d27e63aa32a5aa213bad4d1ddadca3c7654a118644ca4349bf276c38be3d"
        const payload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'refresh_token',
            refresh_token: refreshToken
        };

        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': FORWARDED_HOST
        };

        const response = http.post(`${BASE_URL}/oauth/token`, payload, {
            headers,
            tags: { url: '/oauth/token' }
        });

        return check(response, {
            'oauth-refresh-token status is 200 or 400': (r) => r.status === 200 || r.status === 400, // 400 expected if refresh token is invalid
            'oauth-refresh-token response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
            'oauth-refresh-token has proper response': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined && body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
        });
    },

    oauthTokenAuthorizationCode: () => {
        const userToken = getOAuthUserToken("Penny", "BigBreakaway");
        if (!userToken) {
            console.error('Failed to get user token for authorization code flow');
            return false;
        }

        const redirectUri = "https://jellymarr.io/";
        const authorizePayload = {
            response_type: 'code',
            client_id: __ENV.CLIENT_ID,
            redirect_uri: redirectUri,
            state: 'test-state-123'
        };

        const authorizeHeaders = {
            ...addAuthHeader(userToken),
            'Content-Type': 'application/json'
        };

        const authorizeResponse = http.post(`${BASE_URL}/oauth/authorise`, JSON.stringify(authorizePayload), {
            headers: authorizeHeaders,
            tags: { url: '/oauth/authorise' }
        });

        if (authorizeResponse.status !== 200) {
            console.error(`Failed to get authorization code: ${authorizeResponse.status} ${authorizeResponse.body}`);
            return false;
        }

        let authorizationCode;
        try {
            const authorizeBody = JSON.parse(authorizeResponse.body as string);
            authorizationCode = authorizeBody.code;
        } catch {
            console.error('Failed to parse authorization response');
            return false;
        }

        if (!authorizationCode) {
            console.error('No authorization code received');
            return false;
        }

        const tokenPayload = {
            client_id: __ENV.CLIENT_ID,
            client_secret: __ENV.CLIENT_SECRET,
            grant_type: 'authorization_code',
            code: authorizationCode,
            redirect_uri: redirectUri
        };

        const tokenHeaders = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-forwarded-host': FORWARDED_HOST
        };

        const tokenResponse = http.post(`${BASE_URL}/oauth/token`, tokenPayload, {
            headers: tokenHeaders,
            tags: { url: '/oauth/token' }
        });

        return check(tokenResponse, {
            'oauth-authorization-code authorize-step status is 200': () => authorizeResponse.status === 200,
            'oauth-authorization-code token-step status is 200': (r) => r.status === 200,
            'oauth-authorization-code has access_token': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.access_token !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
            'oauth-authorization-code has refresh_token': (r) => {
                if (r.status === 200) {
                    try {
                        const body = JSON.parse(r.body as string);
                        return body.refresh_token !== undefined;
                    } catch {
                        return false;
                    }
                }
                return false;
            },
            'oauth-authorization-code response time': (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    healthcheck: () => {
        const response = http.get(`${BASE_URL}/healthcheck`, {
            headers: DEFAULT_HEADERS,
            tags: { url: '/healthcheck' }
        });
        return check(response, {
            'healthcheck status is 200': (r) => r.status === 200,
            "health check response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getJwks: () => {
        const response = http.get(`${BASE_URL}/v1/jwks`, {
            headers: DEFAULT_HEADERS,
            tags: { url: '/v1/jwks' }
        });
        return check(response, {
            'get-jwks status is 200': (r) => r.status === 200,
            "get-jwks response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    childAge: () => {
        const dob = randomDob();
        const country = randomCountry();
        const url = `${BASE_URL}/v1/countries/child-age?country=${country}&dob=${dob}`;
        const response = http.get(url, {
            headers: DEFAULT_HEADERS,
            tags: { url: '/v1/countries/child-age' }
        });

        return check(response, {
            'child-age status is 200': (r) => r.status === 200,
            'child-age response time': (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    checkUsername: () => {
        const username = `TestUser${generateRandomString().slice(0, 5)}`;
        const response = http.get(`${BASE_URL}/v1/users/check-username?username=${username}`, {
            headers: DEFAULT_HEADERS,
            tags: { url: '/v1/users/check-username' }
        });
        return check(response, {
            'check-username status is 200': (r) => r.status === 200,
            "check-username response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    createUser: (token: string) => {
        const payload = processPayload({
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'load-test-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v2/apps/:appId/users' }
        });
        return check(response, {
            'create-user status is 201': (r) => r.status === 201,
            "create-user response time": (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    createUserV2: (token: string) => {
        const payload = processPayload({
            username: "load-test-{random}",
            password: TEST_PASSWORD,
            parentEmail: "load-test-{random}@epicgames.com",
            originAppId: parseInt(JAVI_TEST_APP_ID),
            token: "test-token",
            dateOfBirth: "2022-05-13"
        });

        const headers = {
            ...addAuthHeader(token),
            "x-kws-bypass-talon": "true"
        };

        const response = http.post(`${BASE_URL}/v2/users`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v2/users' }
        });
        return check(response, {
            'create-user-v2 status is 201': (r) => r.status === 201,
            "create-user-v2 response time": (r) => r.timings.duration < TARGET_TIME_CREATION_REQUEST,
        });
    },

    getUser: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}`, {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId' }
        });
        return check(response, {
            'get-user status is 200': (r) => r.status === 200,
            "get-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    activateUser: (token: string) => {
        const user = createFreshUserV2(token);

        if (!user) {
            console.error('Failed to create fresh user for activateUser');
            return false;
        }

        const payload = {
            appName: 'javi-test',
            permissions: ['Stranger.things', 'Greece.Opt-out'],
            dateOfBirth: '2010-01-01',
            parentEmail: processPayload('load-test-activate-{random}@epicgames.com')
        };

        const userToken = getOAuthUserToken(user.username, TEST_PASSWORD);
        if (!userToken) {
            console.error('Failed to get user token for activateUser');
            return false;
        }
        const headers = addAuthHeader(userToken);
        const response = http.post(`${BASE_URL}/v1/users/${user.id}/apps`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v1/users/:userId/apps' }
        });
        return check(response, {
            'activate-user status is 200 or 201': (r) => r.status === 200 || r.status === 201,
            'activate-user has fresh user': () => user !== undefined,
            "activate-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    activateUserV2: (token: string, domCraftToken: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for activateUserV2');
            return false;
        }
        const userId = user.id;


        const payload = processPayload({
            permissions: ['permissionA'],
            parentEmail: 'load-test-activate-{random}@epicgames.com'
        });

        const headers = addAuthHeader(domCraftToken);
        const response = http.post(`${BASE_URL}/v2/apps/${DOM_CRAFT_APP_ID}/users/${userId}/activate`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/activate' }
        });
        return check(response, {
            'activate-user-v2 status is 200 or 201': (r) => r.status === 200 || r.status === 201,
            'activate-user-v2 has fresh user': () => userId !== undefined,
            "activate-user-v2 response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    requestPermissions: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for requestPermissions');
            return false;
        }
        const payload = processPayload({
            parentEmail: 'load-test-permissions-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}/request-permissions`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/request-permissions' }
        });
        return check(response, {
            'request-permissions status is 200': (r) => r.status === 200,
            "request-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getPermissions: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/permissions`, {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/permissions' }
        });
        return check(response, {
            'get-permissions status is 200': (r) => r.status === 200,
            "get-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    getPermissionsExtended: (token: string) => {
        const userId = getRandomUserId();
        const headers = addAuthHeader(token);
        const response = http.get(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${userId}/permissions?extended=true`, {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/permissions' }
        });
        return check(response, {
            'get-permissions-extended status is 200': (r) => r.status === 200,
            "get-permissions-extended response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    reviewPermissions: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            throw new Error('Could not create user');
        }
        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}/review-permissions`, JSON.stringify({}), {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/review-permissions' }
        });
        return check(response, {
            'review-permissions status is 201': (r) => r.status === 201,
            "review-permissions response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    updateParentEmail: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            throw new Error('Could not create user');
        }
        const payload = processPayload({
            parentEmail: 'load-test-update-{random}@epicgames.com',
        });

        const headers = addAuthHeader(token);
        const response = http.post(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}/update-parent-email`, JSON.stringify(payload), {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId/update-parent-email' }
        });
        return check(response, {
            'update-parent-email status is 204': (r) => r.status === 204,
            "update-parent-email response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },

    deleteUserActivation: (token: string) => {
        const user = createFreshUserV2Apps(token);
        if (!user) {
            console.error('Failed to create fresh user for deleteUser');
            return false;
        }
        const headers = addAuthHeader(token);
        const response = http.del(`${BASE_URL}/v2/apps/${JAVI_TEST_APP_ID}/users/${user.id}`, null, {
            headers,
            tags: { url: '/v2/apps/:appId/users/:userId' }
        });
        return check(response, {
            'delete-user status is 204': (r) => r.status === 204,
            "delete-user response time": (r) => r.timings.duration < TARGET_TIME_OTHER_REQUEST,
        });
    },
};

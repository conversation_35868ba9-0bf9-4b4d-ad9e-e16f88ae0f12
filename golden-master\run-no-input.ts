import { AxiosError } from 'axios';
import chalk from 'chalk';

import { config, StepData, EndpointConfig, WorkflowConfig } from './config';
import { endPointString, getFullUrl, printTestSummary } from './log-utils';
import { compareResponses, makeRequest, sanitizeResponse } from './test-utils';

async function handleError(error: Error, context: string) {
    if (error instanceof AxiosError && error.response) {
        const { status, statusText, data } = error.response;
        console.log(chalk.red(`Error at ${context}: ${getFullUrl(error.request)} -- ${status} ${statusText}`));
        console.log(chalk.red(JSON.stringify(data, null, 2)));
    } else {
        console.log(chalk.red(`✗ Error ${context}: ${error.message}\n`), error);
    }
}

async function testEndpointPair(sourceEndpoint: EndpointConfig, targetEndpoint: EndpointConfig, stepName?: string) {
    const displayName = stepName || endPointString(sourceEndpoint);
    console.log(chalk.cyan(`Testing ${displayName}`));

    const sourceResponse = await makeRequest(config.classicApi, sourceEndpoint);
    const targetResponse = await makeRequest(config.wrapperApi, targetEndpoint);

    const sourceSanitized = sanitizeResponse(sourceResponse, sourceEndpoint.bodyFieldConditions, sourceEndpoint.skipHeaders, sourceEndpoint.sortKey);
    const targetSanitized = sanitizeResponse(targetResponse, targetEndpoint.bodyFieldConditions, targetEndpoint.skipHeaders, targetEndpoint.sortKey);

    const isMatch = await compareResponses(sourceSanitized, targetSanitized);

    if (!isMatch) {
        console.log(chalk.red(`🔗 Full target URL: ${getFullUrl(targetResponse.request)}\n`));
    }

    return { isMatch, sourceResponse, targetResponse };
}

async function testSingleEndpoint(endpoint: EndpointConfig) {
    try {
        if (endpoint.name) {
            console.log(chalk.cyan(`🧪 Name: ${endpoint.name}`));
        }

        // Same endpoint, because there's no special state between endpoints, unlike workflows
        const { isMatch } = await testEndpointPair(endpoint, endpoint);
        return { passed: isMatch };
    } catch (error) {
        await handleError(error as Error, 'testing endpoint');
        return { passed: false, error: error as Error };
    }
}

function updateWorkflowState(currentState: any, newState: any): any {
    return { ...currentState, ...newState };
}

function createStepData(response: any, endpoint: EndpointConfig, workflowState: any) {
    return {
        resBody: response.data,
        params: endpoint.params || {},
        prevWorkflowState: workflowState
    };
}

async function testWorkflow(workflow: WorkflowConfig) {
    console.log(chalk.cyan(`⚙️ Starting workflow: ${workflow.name || 'Unnamed workflow'}`));

    let classicStepData: StepData = { resBody: {}, params: {}, prevWorkflowState: {} };
    let wrapperStepData: StepData = { resBody: {}, params: {}, prevWorkflowState: {} };

    for (const [index, step] of workflow.workflow.entries()) {
        try {
            const sourceEndpoint = step(classicStepData);
            const targetEndpoint = step(wrapperStepData);
            const stepName = `Step ${index + 1}: ${endPointString(sourceEndpoint)}`;

            const {
                isMatch,
                sourceResponse,
                targetResponse
            } = await testEndpointPair(sourceEndpoint, targetEndpoint, stepName);

            if (!isMatch) {
                console.log(chalk.red(`❌  ${stepName} failed`));
                return { passed: false };
            }

            console.log(chalk.green(`✅  ${stepName} passed\n`));

            // Update workflow state and step data for next iteration
            const classicWorkflowState = updateWorkflowState(classicStepData.prevWorkflowState, sourceEndpoint.workflowState);
            const wrapperWorkflowState = updateWorkflowState(wrapperStepData.prevWorkflowState, targetEndpoint.workflowState);

            classicStepData = createStepData(sourceResponse, sourceEndpoint, classicWorkflowState);
            wrapperStepData = createStepData(targetResponse, targetEndpoint, wrapperWorkflowState);

        } catch (error) {
            await handleError(error as Error, `testing workflow step ${index + 1}`);
            return { passed: false, error: error as Error };
        }
    }

    console.log(chalk.green(`✅  Workflow passed`));
    return { passed: true };
}

function isWorkflowConfig(endpoint: EndpointConfig | WorkflowConfig): endpoint is WorkflowConfig {
    return 'workflow' in endpoint && Array.isArray(endpoint.workflow);
}

async function runTest(endpoint: EndpointConfig | WorkflowConfig) {
    return isWorkflowConfig(endpoint) ? await testWorkflow(endpoint) : await testSingleEndpoint(endpoint);
}

async function run() {
    console.log(chalk.blue('\nStarting Golden Master API Tests\n'));

    let passCount = 0;
    let failCount = 0;

    // Run tests sequentially to avoid conflicts between workflows
    for (const endpoint of config.endpoints) {
        const result = await runTest(endpoint);
        result.passed ? passCount++ : failCount++;
    }

    printTestSummary(passCount, failCount);

    if (failCount > 0) {
        process.exit(1);
    }
}

if (require.main === module) {
    run().catch(console.error);
}

import { Tiso6391, Tiso31662 } from '@superawesome/freekws-regional-config';

import { User } from './user.entity';

export type UserCreateParams = {
  orgEnvId: string;
  appId: number;
  dateOfBirth?: string;
  language: Tiso6391;
  signUpCountry?: Tiso31662;
};
export const UserUpdateFields = ['dateOfBirth', 'username', 'language', 'signUpCountry', 'externalId'] as const;

export type UserUpdateParams = Partial<Pick<User, (typeof UserUpdateFields)[number]>>;

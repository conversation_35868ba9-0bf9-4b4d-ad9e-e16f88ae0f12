import { App } from '../../src/app/app.entity';
import { OrgEnv } from '../../src/org-env/org-env.entity';

const APPS: App[] = [
  {
    id: 1852416263,
    orgEnv: { id: 'e67fed5f-da21-4a5c-b1bd-e35bfffe7101' } as OrgEnv,
    orgEnvId: 'e67fed5f-da21-4a5c-b1bd-e35bfffe7101',
    productId: '7794e949-9fed-43eb-80be-4b504dc43b1b',
    productEnvId: '7a6002f7-12dd-471a-88f2-da8f5317645d',
    name: 'test-app',
    mode: 'dev',
    oauthClientId: 'b3e00544-5b64-48ab-a7a9-48e1a1dfa062',
    apiKey: 'top-secret',
    mobileApiKey: 'amtarasu',
    translations: [],
    activations: [],
    oauthCallbackUrls: [],
    termsAndConditionsRequired: false,
    createdAt: new Date('2021-11-17T12:17:28.265Z'),
    updatedAt: new Date('2021-11-17T12:17:28.265Z'),
  },
];

export default APPS;

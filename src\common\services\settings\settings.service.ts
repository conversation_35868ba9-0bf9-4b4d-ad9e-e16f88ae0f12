import { HttpStatus, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AuthLibraryUtils, EKeycloakScope, KeycloakService } from '@superawesome/freekws-auth-library';
import { Permissions, ResultPermission } from '@superawesome/freekws-classic-wrapper-common';
import { NestJsClient } from '@superawesome/freekws-clients-nestjs';
import { HttpRequestConfig } from '@superawesome/freekws-common-http-client';
import { SaApiResponseErrorDto } from '@superawesome/freekws-nestjs-http-interceptors';
import { CountryRegionExists, Tiso31662 } from '@superawesome/freekws-regional-config';
import {
  DeletePreferredValueSettingsDTO,
  EResponseFormat,
  ESettingConsentType,
  SettingIdentifierDTO,
  SETTINGS_BACKEND_API_CLIENT_INJECT_KEY,
  settingsBackendPlugin,
  UserSettingValueDTO,
  UserSettingValueShortDTO,
} from '@superawesome/freekws-settings-common';
import { ValidationArguments } from 'class-validator';
import _, { isNil, omitBy } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { Repository } from 'typeorm';
import { parse } from 'yaml';

import {
  ESettingsServiceErrorCodes,
  SettingsErrorResponse,
  SettingsServiceConsentNotRequestedError,
  SettingsServiceParentMissingError,
  SettingsServiceParentNotInFamilyError,
  TGetUserSettings,
  TRevokeUserPermissionsParams,
  TSendConsentEmailParams,
} from './types';
import { App } from '../../../app/app.entity';
import { SettingsConfiguration } from '../../../app/types';
import { IClientCredentials } from '../../../org-env/types';
import { isAxiosError } from '../../../utils';
import { ClientKeycloakService } from '../keycloak/client-keycloak.service';
import { KEYCLOAK_PROVIDER } from '../keycloak/keycloak.module';

const DEFAULT_FREEKWS_NAMESPACE = 'default';
const DEFAULT_T_AND_C_NAMESPACE = 'terms';
const DEFAULT_T_AND_C_SETTING_NAME = 'app-terms-and-conditions';
export const DEFAULT_T_AND_C_PERMISSION_NAME = `${DEFAULT_T_AND_C_NAMESPACE}.${DEFAULT_T_AND_C_SETTING_NAME}`;

type GetProductYamlParams = {
  productId: string;
  productEnvId: string;
  orgId: string;
  orgEnvId: string;
};

let settingScopedServiceAccessToken: string | undefined;

@Injectable()
@Span()
export class SettingsService {
  constructor(
    private readonly clientKeycloakService: ClientKeycloakService,
    @Inject(KEYCLOAK_PROVIDER)
    private readonly keycloakService: KeycloakService,
    @Inject(SETTINGS_BACKEND_API_CLIENT_INJECT_KEY)
    private readonly client: NestJsClient<typeof settingsBackendPlugin>,
    @InjectRepository(App) private readonly appRepo: Repository<App>,
  ) {}

  private static transformPermissionToSettingIdentifier(permission: string): SettingIdentifierDTO {
    const parts = permission.split('.');
    const namespace = parts.length === 1 ? DEFAULT_FREEKWS_NAMESPACE : parts[0];
    const settingName = parts[1] ?? parts[0];
    return { namespace, settingName };
  }

  private static transformPermissionsToSettingIdentifiers(permissions: string[]): SettingIdentifierDTO[] {
    return permissions.map((permission) => SettingsService.transformPermissionToSettingIdentifier(permission));
  }

  static isTermsAndConditionsSetting(setting: { namespace: string; settingName: string }) {
    return setting.namespace === DEFAULT_T_AND_C_NAMESPACE && setting.settingName === DEFAULT_T_AND_C_SETTING_NAME;
  }

  static removeTermsAndConditionsSetting<
    T extends {
      namespace: string;
      settingName: string;
    },
  >(freeKWSSettings?: T[]) {
    if (!freeKWSSettings) {
      return [];
    }
    return freeKWSSettings.filter((userSetting) => !this.isTermsAndConditionsSetting(userSetting));
  }

  static isOptIn(setting?: UserSettingValueDTO): boolean {
    if (!setting) {
      return false;
    }
    return (
      setting.definition.ageBracket.consentType === ESettingConsentType.OPT_IN_VERIFIED ||
      setting.definition.ageBracket.consentType === ESettingConsentType.OPT_IN_UNVERIFIED
    );
  }

  static isOptOut(setting?: Pick<UserSettingValueDTO, 'definition'>): boolean {
    if (!setting) {
      return false;
    }
    return setting.definition.ageBracket.consentType === ESettingConsentType.OPT_OUT;
  }

  static transformSettingsToPermissions(settings: UserSettingValueDTO[], permissions: string[]): Permissions {
    const resultPermissions: Record<string, boolean | null> = {};

    for (const permission of permissions) {
      const { namespace, settingName } = SettingsService.transformPermissionToSettingIdentifier(permission);
      const setting = settings.find((s) => s.namespace === namespace && s.settingName === settingName);
      if (setting) {
        resultPermissions[permission] = this.classicValue(setting);
      } else {
        resultPermissions[permission] = null;
      }
    }

    return resultPermissions;
  }

  static transformSettingsToGroupedPermissions(settings: UserSettingValueDTO[], permissions: string[], lang?: string) {
    // Create a map of settings by their full name for easier lookup
    const settingsByName = settings.reduce(
      (acc, cur) => ({
        ...acc,
        [`${cur.namespace}.${cur.settingName}`]: cur,
        [cur.settingName]: cur,
      }),
      {} as Record<string, UserSettingValueDTO | undefined>,
    );

    const notFoundPermissions = permissions.filter((permission) => !settingsByName[permission]);
    if (notFoundPermissions.length > 0) {
      throw new NotFoundException(`Permissions not found: ${notFoundPermissions.join(', ')}`);
    }

    const categorizedPermissions = permissions.reduce(
      (result, permission) => {
        const setting = settingsByName[permission];
        const isGranted = this.classicValue(setting);

        // Already granted permissions: settings that are already enabled
        if (isGranted) {
          result.alreadyGrantedPerms.push(permission);
        }
        // Automatically granted permissions: opt-out settings that are disabled
        else if (SettingsService.isOptOut(setting)) {
          result.automaticallyGrantedPerms.push(permission);
        }
        // Missing permissions: anything not granted
        else {
          result.missingPermissions.push(permission);
        }

        return result;
      },
      {
        missingPermissions: [] as string[],
        alreadyGrantedPerms: [] as string[],
        automaticallyGrantedPerms: [] as string[],
      },
    );

    return {
      missingPermissions: categorizedPermissions.missingPermissions.map((permission) =>
        SettingsService.transformSettingToResultPermission(settingsByName[permission], lang),
      ),
      alreadyGrantedPerms: categorizedPermissions.alreadyGrantedPerms.map((permission) =>
        SettingsService.transformSettingToResultPermission(settingsByName[permission], lang),
      ),
      automaticallyGrantedPerms: categorizedPermissions.automaticallyGrantedPerms.map((permission) =>
        SettingsService.transformSettingToResultPermission(settingsByName[permission], lang),
      ),
    };
  }

  static settingToPermissionName(setting: { namespace: string; settingName: string }): string {
    const { namespace, settingName } = setting;
    if (namespace === DEFAULT_FREEKWS_NAMESPACE) {
      return settingName;
    } else {
      return `${namespace}.${settingName}`;
    }
  }

  private static transformSettingToResultPermission(setting?: UserSettingValueDTO, lang = 'en'): ResultPermission {
    if (!setting) {
      throw new Error(`Trying to map undefined setting to permission`);
    }
    const translation = setting.definition.translations[lang];
    if (!translation) {
      throw new Error(`Translation not found for ${lang}`);
    }
    const description = this.getDescription(setting, lang);
    return {
      name: this.settingToPermissionName(setting),
      displayName: translation.label,
      description: description,
    };
  }

  private static getDescription(setting: UserSettingValueDTO, lang: string) {
    const parentNotice = setting.definition.translations[lang].parentNotice;
    if (parentNotice) {
      const descriptionSplit = parentNotice.split('\n\n');
      if (descriptionSplit && descriptionSplit?.length > 1) {
        return descriptionSplit.slice(0, -1).join('\n\n'); // Remove privacy policy FreeKWS adds
      } else {
        return parentNotice;
      }
    }
  }

  static classicValue(
    setting: Pick<UserSettingValueDTO, 'effectiveValue' | 'parentLimitUpdatedAt' | 'definition'> | undefined,
  ) {
    if (!setting) {
      return null;
    }
    const { effectiveValue, parentLimitUpdatedAt } = setting;
    if (this.isOptOut(setting) && !parentLimitUpdatedAt) {
      return true;
    }
    if (!parentLimitUpdatedAt) {
      return null;
    }
    return Boolean(effectiveValue);
  }

  private static classicPermissionName(namespace: string, settingName: string) {
    if (namespace === DEFAULT_FREEKWS_NAMESPACE) {
      return settingName;
    } else {
      return `${namespace}.${settingName}`;
    }
  }

  static transformSettingsToAllPermissions(settings: UserSettingValueDTO[]): Permissions {
    const settingsNoTAndC = this.removeTermsAndConditionsSetting(settings);
    /* eslint-disable no-param-reassign */
    return settingsNoTAndC.reduce((permissions, setting) => {
      const { namespace, settingName } = setting;
      const permissionName = this.classicPermissionName(namespace, settingName);
      if (setting.parentLimitUpdatedAt) {
        permissions[permissionName] = this.classicValue(setting);
      } else {
        permissions[permissionName] = null;
      }
      return permissions;
    }, {} as Permissions);
    /* eslint-enable no-param-reassign */
  }

  async sendConsentEmail(
    requestParams: TSendConsentEmailParams,
    credentials: IClientCredentials,
  ): Promise<UserSettingValueShortDTO[]> {
    const token = await this.clientKeycloakService.getClientToken(credentials);
    let settingsToRequest: SettingIdentifierDTO[] = [];

    if (requestParams.settings) {
      settingsToRequest = requestParams.settings;
    } else {
      settingsToRequest = SettingsService.transformPermissionsToSettingIdentifiers(requestParams.permissions);
    }

    const app = await this.appRepo.findOneOrFail({
      where: {
        productId: requestParams.productId,
        orgEnvId: credentials.clientId,
      },
      relations: {
        orgEnv: true,
      },
    });

    const settingsConfigurations = await this.getProductSettingsDefinition({
      orgId: app.orgEnv.orgId,
      orgEnvId: app.orgEnv.id,
      productId: app.productId,
      productEnvId: app.productEnvId,
    });

    const settingDefinitions = settingsConfigurations.flatMap((settingsConfiguration) => {
      return settingsConfiguration.settings.map((setting) => {
        return {
          namespace: settingsConfiguration.namespace,
          settingName: setting.settingName,
          irrevocable: setting.irrevocable,
        };
      });
    });

    const irrevocableSettings = settingDefinitions.filter((setting) => setting.irrevocable);
    const existingSettings = await this.getUserSettings(
      {
        ...requestParams,
        dateOfBirth: requestParams.dob,
      },
      credentials,
    );

    for (const irrevocableSetting of irrevocableSettings) {
      const existingIrrevocableSetting = existingSettings.find(
        (setting) =>
          setting.namespace === irrevocableSetting.namespace && setting.settingName === irrevocableSetting.settingName,
      );
      if (existingIrrevocableSetting && existingIrrevocableSetting.effectiveValue === true) {
        continue;
      }
      settingsToRequest.push({
        namespace: irrevocableSetting.namespace,
        settingName: irrevocableSetting.settingName,
      });
    }

    const existingNotIrrevocableSettings = existingSettings.filter(
      (setting) =>
        !irrevocableSettings.some(
          (irrevocableSettings) =>
            setting.namespace === irrevocableSettings.namespace &&
            setting.settingName === irrevocableSettings.settingName,
        ),
    );

    for (const existingNotIrrevocableSetting of existingNotIrrevocableSettings) {
      if (
        !settingsToRequest.some(
          (settings) =>
            settings.namespace === existingNotIrrevocableSetting.namespace &&
            settings.settingName === existingNotIrrevocableSetting.settingName,
        )
      ) {
        settingsToRequest.push({
          namespace: existingNotIrrevocableSetting.namespace,
          settingName: existingNotIrrevocableSetting.settingName,
        });
      }
    }

    let requestAge: 0 | undefined = void 0;
    if (requestParams.dob === '' || requestParams.dob === undefined) {
      requestAge = 0;
    }

    try {
      const {
        data: {
          response: { settings },
        },
      } = await this.client.getModule('userSettingValue').sendConsentEmailAtProductLevel(
        {
          params: {
            userId: `${requestParams.userId}`,
            productId: `${requestParams.productId}`,
          },
          body: {
            parentEmail: requestParams.parentEmail,
            language: requestParams.language,
            location: requestParams.location
              ? SettingsService.convertUnsupportedCountryCode(requestParams.location)
              : requestParams.location,
            dob: requestParams.dob,
            age: requestAge,
            settings: this.removeDuplicateSettings(settingsToRequest),
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      return settings;
    } catch (error) {
      if (isAxiosError<{ error: { message?: string } }>(error)) {
        const errorMessage = error.response?.data?.error?.message;
        if (
          typeof errorMessage === 'string' &&
          (errorMessage.includes('does not have any existing settings') ||
            /Setting value for .* does not exist/.test(errorMessage))
        ) {
          throw new NotFoundException('A passed setting does not exist.', {
            cause: { requestedSettings: settingsToRequest },
          });
        }
      }
      if (error.response?.data?.freekwsErrorCode === ESettingsServiceErrorCodes.CONSENT_REQUEST_MISSING_PARENT_EMAIL) {
        throw new SettingsServiceParentMissingError(ESettingsServiceErrorCodes.CONSENT_REQUEST_MISSING_PARENT_EMAIL, {
          cause: error,
        });
      }
      throw error;
    }
  }

  private removeDuplicateSettings(settings: { namespace: string; settingName: string }[]) {
    return _.uniqBy(settings, (s) => `${s.namespace}::${s.settingName}`);
  }

  async getUserSettings(
    { userId, productId, location, dateOfBirth }: TGetUserSettings,
    credentials: IClientCredentials,
  ): Promise<UserSettingValueDTO[]> {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    let requestAge: 0 | undefined = void 0;
    if (dateOfBirth === '' || dateOfBirth === undefined) {
      requestAge = 0;
    }

    try {
      const {
        data: {
          response: { settings },
        },
      } = await this.client.getModule('userSettingValue').getSettingsForUserAtProductLevel(
        {
          params: {
            userId: `${userId}`,
            productId,
          },
          query: omitBy(
            {
              location: location ? SettingsService.convertUnsupportedCountryCode(location) : location,
              dob: dateOfBirth,
              format: EResponseFormat.FULL,
              age: requestAge,
            },
            isNil,
          ),
        },
        this.buildHeaders(token),
      );

      return settings as UserSettingValueDTO[];
    } catch (error) {
      if (
        isAxiosError<SaApiResponseErrorDto<string>>(error) &&
        error.response?.data?.error?.statusCode === HttpStatus.NOT_FOUND
      ) {
        return [];
      }
      throw error;
    }
  }

  async deleteUserSettingsAtProductLevel(
    userId: number,
    productId: string,
    settings: DeletePreferredValueSettingsDTO[],
    credentials: IClientCredentials,
  ) {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    const res = await this.client.getModule('userSettingValue').deletePreferredValueRequestAtProductLevel(
      {
        params: {
          userId: `${userId}`,
          productId: `${productId}`,
        },
        body: {
          settings,
        },
      },
      this.buildHeaders(token),
    );

    return res.data.response;
  }

  async deleteUser(userId: number, credentials: IClientCredentials) {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    await this.client.getModule('userSettingValue').deleteUserRequest(
      {
        params: {
          userId: `${userId}`,
        },
      },
      this.buildHeaders(token),
    );
  }

  async resendConsentEmail(
    { userId, productId, ...consentRequestParams }: TRevokeUserPermissionsParams,
    credentials: IClientCredentials,
  ) {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    const {
      data: {
        response: { settings },
      },
    } = await this.client.getModule('userSettingValue').getSettingsForUserAtProductLevel(
      {
        params: {
          userId: `${userId}`,
          productId: productId,
        },
        query: {
          format: EResponseFormat.SHORT,
        },
      },
      this.buildHeaders(token),
    );

    const consentedSettings = settings.filter((setting) => !!setting.consentRequestedAt);

    if (consentedSettings.length > 0) {
      try {
        const {
          data: {
            response: { consents },
          },
        } = await this.client.getModule('userSettingValue').sendConsentEmailAtProductLevel(
          {
            params: {
              userId: `${userId}`,
              productId: `${productId}`,
            },
            body: {
              parentEmail: consentRequestParams.parentEmail,
              language: consentRequestParams.language,
              location: consentRequestParams.location
                ? SettingsService.convertUnsupportedCountryCode(consentRequestParams.location)
                : consentRequestParams.location,
              dob: consentRequestParams.dob,
              settings: consentedSettings.map(({ namespace, settingName }) => ({ namespace, settingName })),
            },
          },
          this.buildHeaders(token),
        );
        if (consents.length === 0) {
          throw new SettingsServiceConsentNotRequestedError();
        }
      } catch (error) {
        if (
          isAxiosError<SettingsErrorResponse>(error) &&
          error.response?.data.error.errorCode ===
            ESettingsServiceErrorCodes.CONSENT_REQUEST_PARENT_NOT_PART_OF_CHILD_FAMILY
        ) {
          throw new SettingsServiceParentNotInFamilyError();
        }
        throw error;
      }
    }
  }

  async sendGenerateConsentRequest(userId: number, productId: string, credentials: IClientCredentials): Promise<void> {
    const token = await this.clientKeycloakService.getClientToken(credentials);

    await this.client.getModule('userSettingValue').generateConsentRequestAtProductLevelRequest(
      {
        params: {
          userId: `${userId}`,
          productId: `${productId}`,
        },
      },
      this.buildHeaders(token),
    );
  }

  async getProductSettingsDefinition({ productId, productEnvId, orgId, orgEnvId }: GetProductYamlParams) {
    let token = settingScopedServiceAccessToken;
    if (!token || !AuthLibraryUtils.isTokenValid(token)) {
      token = await this.keycloakService.getScopedAccessToken([EKeycloakScope.SETTINGS]);
      settingScopedServiceAccessToken = token;
    }

    const { data } = await this.client.getModule('setting').getInternalAdminSettingDefintionsYamlAtProductLevel(
      {
        params: {
          orgId,
          orgEnvId,
          productId,
          productEnvId,
        },
        query: {},
      },
      this.buildHeaders(token),
    );

    return data.response.definitions.map<SettingsConfiguration>((doc) => parse(doc));
  }

  private buildHeaders(token: string): HttpRequestConfig {
    return {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    } as HttpRequestConfig;
  }

  static convertUnsupportedCountryCode(countryCode?: string) {
    const defaultCountryCode = 'ZZ' as Tiso31662;

    if (!countryCode) {
      return defaultCountryCode;
    }

    if (SettingsService.isValidCountryCode(countryCode)) {
      return countryCode;
    }
    return defaultCountryCode;
  }

  static isValidCountryCode(countryCode: string): countryCode is Tiso31662 {
    const validationArgs: ValidationArguments = {
      value: countryCode,
      constraints: [{ case: false, optional: true }],
      targetName: '',
      object: {},
      property: '',
    };

    return new CountryRegionExists().validate(countryCode, validationArgs);
  }
}

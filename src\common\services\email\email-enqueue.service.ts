﻿import { Injectable, Inject, HttpStatus } from '@nestjs/common';
import { BrandingDTO, CustomCopyDTO } from '@superawesome/freekws-branding-service-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import {
  FreeKWSEmailService,
  TBaseEmailData,
  EEmailBuilderKind,
  TUserForgottenPasswordEmailData,
  TUserChildChangedPasswordEmailData,
} from '@superawesome/freekws-email-builder';
import { Producer } from '@superawesome/freekws-kafka';
import { SaApiResponseErrorDto } from '@superawesome/freekws-nestjs-http-interceptors';
import { EmailMessageService, EmailDTO } from '@superawesome/freekws-queue-messages/email';
import { EmailTagsDTO } from '@superawesome/freekws-queue-messages/email/email.dto';
import { AxiosError } from 'axios';

import { IEnqueueChildChangedPasswordEmailParams, IEnqueueForgottenPasswordEmailParams } from './types';
import { BrandingService } from '../branding/branding.service';
import { ConfigService } from '../config/config.service';
import { KAFKA_PRODUCER } from '../kafka/kafka-producer.module';

@Injectable()
export class EmailEnqueueService {
  private readonly emailTopic: string;

  constructor(
    private configService: ConfigService,
    private emailMessageService: EmailMessageService,
    private freeKwsEmailService: FreeKWSEmailService,
    @Inject(KAFKA_PRODUCER) private kafkaProducer: Producer,
    private readonly brandingService: BrandingService,
    private readonly logger: SALogger,
  ) {
    this.emailMessageService.setKafkaProducer(this.kafkaProducer);
    this.emailTopic = this.configService.getEmailService().kafkaTopic;
  }

  public async enqueueForgottenPasswordEmail(
    orgId: string,
    orgName: string,
    orgEnvId: string,
    emailParams: IEnqueueForgottenPasswordEmailParams,
    translation: BrandingDTO,
    mode: string,
  ): Promise<void> {
    const baseEmail: TBaseEmailData = {
      footer: {
        logoSrc: this.configService.getEmailService().footer.logoSrc,
      },
      header: {
        src: translation.bannerUrl ?? '',
      },
      kwsPrivacyPolicyLink: this.configService.getLinks().kwsPrivacyPolicyLink,
      kwsTermsLink: this.configService.getLinks().kwsTermsLink,
      appName: translation.name,
      orgName,
    };

    const data: TUserForgottenPasswordEmailData = {
      ...baseEmail,
      kwsFaqLink: this.configService.getLinks().kwsFaqLink,
      customerPrivacyPolicyLink: translation.privacyPolicyUrl,
      customPrimaryColour: translation.primaryColor,
      ctaLink: emailParams.resetPasswordLink,
      language: emailParams.language,
    };

    const customCopy = await this.getCustomCopy(orgEnvId, emailParams.language);

    const { html, subject, kind } = this.freeKwsEmailService.buildUserForgottenPasswordEmail(
      emailParams.language,
      data,
      customCopy?.terms,
    );

    const emailAttributes: EmailDTO = {
      subject,
      html,
      fromEmail: translation.fromEmailAddress ?? '',
      toEmail: emailParams.toEmailAddress,
      fromName: translation.name,
      tags: this.extractEmailTags(mode, kind),
      metadata: { orgId, orgEnvId },
      orgId,
    };

    return this.enqueueEmail(emailAttributes);
  }

  public async enqueueChildChangedPasswordEmail(
    orgId: string,
    orgName: string,
    orgEnvId: string,
    emailParams: IEnqueueChildChangedPasswordEmailParams,
    translation: BrandingDTO,
    mode: string,
  ): Promise<void> {
    const baseEmail: TBaseEmailData = {
      footer: {
        logoSrc: this.configService.getEmailService().footer.logoSrc,
      },
      header: {
        src: translation.bannerUrl ?? '',
      },
      kwsPrivacyPolicyLink: this.configService.getLinks().kwsPrivacyPolicyLink,
      kwsTermsLink: this.configService.getLinks().kwsTermsLink,
      appName: translation.name,
      orgName,
    };

    const data: TUserChildChangedPasswordEmailData = {
      ...baseEmail,
      kwsFaqLink: this.configService.getLinks().kwsFaqLink,
      customerPrivacyPolicyLink: translation.privacyPolicyUrl,
      customPrimaryColour: translation.primaryColor,
      childUsername: emailParams.childUsername,
      language: emailParams.language,
      supportEmailAddress: emailParams.supportEmailAddress,
    };

    const customCopy = await this.getCustomCopy(orgEnvId, emailParams.language);

    const { html, subject, kind } = this.freeKwsEmailService.buildUserChildChangedPasswordEmail(
      emailParams.language,
      data,
      customCopy?.terms,
    );

    const emailAttributes: EmailDTO = {
      subject,
      html,
      fromEmail: translation.fromEmailAddress ?? '',
      toEmail: emailParams.toEmailAddress,
      fromName: translation.name,
      tags: this.extractEmailTags(mode, kind),
      metadata: { orgId, orgEnvId },
      orgId,
    };

    return this.enqueueEmail(emailAttributes);
  }

  private extractEmailTags(mode: string, kind: EEmailBuilderKind): EmailTagsDTO {
    return {
      repository: 'freekws-classic-wrapper-backend',
      environment: this.configService.config.environment,
      mode: mode,
      kind,
    };
  }

  private async getCustomCopy(orgEnvId: string, language: string): Promise<CustomCopyDTO | undefined> {
    let customCopy: CustomCopyDTO | undefined;
    try {
      customCopy = await this.brandingService.getCustomCopy(orgEnvId, language);
    } catch (error) {
      const axiosError = error as AxiosError<SaApiResponseErrorDto<string>>;
      if (axiosError?.response?.data?.error?.statusCode === HttpStatus.NOT_FOUND) {
        customCopy = undefined;
        this.logger.info(`Custom copy not found for ${orgEnvId}`);
      }
    }
    return customCopy;
  }

  private async enqueueEmail(emailAttributes: EmailDTO): Promise<void> {
    await this.emailMessageService.send(emailAttributes, this.emailTopic);
  }
}

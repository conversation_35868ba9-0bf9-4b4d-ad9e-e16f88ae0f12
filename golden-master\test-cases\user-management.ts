import { EndpointConfig, WorkflowConfig } from '../config';
import { testData } from '../test-data';

export const userManagementTests: (EndpointConfig | WorkflowConfig)[] = [
    {
        name: 'Activating app for user user with an app that is already activated',
        path: '/v1/users/:userId/apps',
        method: 'POST',
        params: {
            userId: testData.users.javiTest.activateAppUser,
        },
        payload: {
            appName: 'javi-test',
            permissions: ['Stranger.things', 'Greece.Opt-out'],
            dateOfBirth: '2010-01-01',
            parentEmail: '<EMAIL>',
        },
        bodyFieldConditions: {
            errorMessage: "present"
        }
    },
    {
        name: 'Create user',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            id: 'present',
            uuid: 'skip',
        },
    },
    {
        name: 'Create user without parent email',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            detail: "skip",
            invalid: "skip",
        },
    },
    {
        name: 'Create user without country',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            detail: "skip",
            invalid: "skip",
        },
    },
    {
        name: 'Create user with an invalid country error case',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'INVALID',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            detail: "skip",
            invalid: "skip",
        },
    },
    {
        name: 'Create user with an invalid country that passes classic validations',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'NT',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            id: "present",
            uuid: "present"
        },
    },
    {
        name: 'Create user with no language',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            id: "present",
            uuid: "present"
        },
    },
    {
        name: 'Create user with invalid language',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'nt',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            id: "present",
            uuid: "present"
        },
    },
    {
        name: 'Create user with no date of birth',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            detail: "skip",
            invalid: "skip",
        },
    },
    {
        name: 'Create user with no parent email',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            detail: "skip",
            invalid: "skip",
        },
    },
    {
        name: 'Create user with no permissions',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
        },
        bodyFieldConditions: {
            id: "present",
            uuid: "present"
        },
    },
    {
        name: 'Create user with empty permissions array',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: [],
        },
        bodyFieldConditions: {
            id: "present",
            uuid: "present"
        },
    },
    {
        name: 'Create user with one invalid permission',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: [
                "abc123"
            ],
        },
        bodyFieldConditions: {
            errorMessage: "present",
        },
    },
    {
        name: 'Create user with more than one invalid permission',
        path: '/v2/apps/:appId/users',
        method: 'POST',
        params: {
            appId: testData.apps.javiTest,
        },
        payload: {
            country: 'GM',
            language: 'en',
            dateOfBirth: '2020-10-10',
            parentEmail: 'golden-eye-0x07-{random}@epicgames.com',
            permissions: [
                "permissionThatDoesNotExist",
                "abc",
                "abc123"
            ],
        },
        bodyFieldConditions: {
            errorMessage: "present",
        },
    },
    {
        path: '/v2/apps/:appId/users/:userId/request-permissions',
        method: 'POST',
        params: {
            userId: testData.users.javiTest.requestPermissionsUser,
        },
        payload: {
            parentEmail: '<EMAIL>',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            'user.parentEmail': 'redacted',
            // parent state flags are all false - see KCMP-248
            'user.parentExpired': 'present',
            'user.parentIdVerified': 'present',
            'user.parentRejected': 'present',
            'user.parentVerified': 'present',
        },
    },
    {
        name: 'Request Permissions with date of birth',
        path: '/v2/apps/:appId/users/:userId/request-permissions',
        method: 'POST',
        params: {
            userId: testData.users.javiTest.userWithDob,
        },
        payload: {
            parentEmail: 'dominic.wild+{random}@xa.epicgames.com',
            dateOfBirth: '2016-09-14',
            permissions: ['whatever-2', 'whatever', 'display-name', 'Stranger.things'],
        },
        bodyFieldConditions: {
            'user.parentEmail': 'redacted',
        },
    },
    {
        name: 'Updating parent email with already verified parent',
        path: '/v2/apps/:appId/users/:userId/update-parent-email',
        params: {
            userId: testData.users.javiTest.userUpdateEmail,
        },
        method: 'POST',
        payload: {
            parentEmail: 'dominic.wild+updateEmail-{random}@xa.epicgames.com',
        },
    },
    {
        name: 'Get User',
        path: '/v2/apps/:appId/users/:userId',
        params: {
            userId: testData.users.javiTest.getUser,
        },
        method: 'GET',
        bodyFieldConditions: {
            activationCreatedAt: 'present',
            uuid: 'present',
            // parent state flags are all false - see KCMP-248
            'parentState.verified': 'present',
            'parentState.idVerified': 'present',
            'parentState.deleted': 'present',
            'parentState.rejected': 'present',
            'parentState.expired': 'present',
        },
    },
    {
        path: '/v2/apps/:appId/users/:userId/permissions',
        params: {
            userId: testData.users.javiTest.getPermissionsUser,
        },
        method: 'GET',
    },
    {
        path: '/v2/apps/:appId/users/:userId/permissions?extended=true',
        params: {
            userId: testData.users.javiTest.getPermissionsUser,
        },
        method: 'GET',
    },
    {
        path: '/v2/apps/:appId/users/:userId/review-permissions',
        params: {
            userId: testData.users.javiTest.reviewPermissionsUser,
        },
        method: 'POST',
    },
    {
        name: 'Checking for username needing review',
        path: '/v1/users/check-username?username=MikeHuntIs4Timmy',
        method: 'GET',
        bodyFieldConditions: {
            "details.reasons": "skip"
        }
    },
    {
        name: 'Checking for username that is accepted',
        path: '/v1/users/check-username?username=Hazel',
        method: 'GET',
    },
    {
        name: 'Checking for username that is rejected',
        path: '/v1/users/check-username?username=Suekondeezknots',
        method: 'GET',
        bodyFieldConditions: {
            "details.reasons": "skip"
        }
    },
    {
        name: 'Checking for username that is taken',
        path: '/v1/users/check-username?username=cthulu',
        method: 'GET',
        bodyFieldConditions: {
            "details.reasons": "skip"
        }
    },
    {
        name: "Regular POST /v2/users request",
        path: '/v2/users',
        method: 'POST',
        payload: {
            username: "golden-sun-{random}",
            password: "master-sword",
            parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
            originAppId: +testData.apps.javiTest,
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
            dateOfBirth: "2022-05-13"
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            id: "present"
        }
    },
    {
        name: "No origin app ID",
        path: '/v2/users',
        method: 'POST',
        payload: {
            username: "golden-sun-{random}",
            password: "master-sword",
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
            dateOfBirth: "2012-05-13"
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            invalid: "skip",
            detail: "skip",
        }
    },
    {
        name: "No date of birth",
        path: '/v2/users',
        method: 'POST',
        payload: {
            username: "golden-sun-{random}",
            password: "master-sword",
            originAppId: +testData.apps.javiTest,
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            invalid: "skip",
            detail: "skip",
        }
    },
    {
        name: "Missing password",
        path: '/v2/users',
        method: 'POST',
        payload: {
            username: "golden-sun-{random}",
            parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
            originAppId: +testData.apps.javiTest,
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
            dateOfBirth: "2012-05-13"
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            invalid: "skip",
            detail: "skip",
        }
    },
    {
        name: "Missing username",
        path: '/v2/users',
        method: 'POST',
        payload: {
            password: "master-sword",
            parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
            originAppId: +testData.apps.javiTest,
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
            dateOfBirth: "2012-05-13"
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            invalid: "skip",
            detail: "skip",
        }
    },
    {
        name: "Missing username and password",
        path: '/v2/users',
        method: 'POST',
        payload: {
            parentEmail: "dominic.wild+post-user-v2-{random}@xa.epicgames.com",
            originAppId: +testData.apps.javiTest,
            token: "someRandomTokenThatIsIgnoredByHeaderButIsRequired",
            dateOfBirth: "2012-05-13"
        },
        headers: {
            "x-kws-bypass-talon": "true"
        },
        bodyFieldConditions: {
            code: "present",
            codeMeaning: "present",
            errorMessage: "present",
            invalid: "skip",
            detail: "skip",
        }
    },
    {
        name: 'Forgot password',
        path: '/v1/users/forgot-password',
        method: 'POST',
        payload: {
            appName: 'javi-test',
            username: 'mvo-forgot1',
        }
    },
]; 
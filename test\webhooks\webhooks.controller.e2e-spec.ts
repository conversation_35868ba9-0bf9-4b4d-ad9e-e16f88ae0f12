import { INestApplication } from '@nestjs/common';
import { EMemberRole } from '@superawesome/freekws-family-service-common';
import {
  FamiliesGroupDeletedPayloadDto,
  FamiliesGuardianRequestExpiredPayloadDto,
  FamiliesUserAddedToFamilyDTO,
  SettingsEffectiveValuesChangedPayloadDTO,
} from '@superawesome/freekws-queue-messages/webhook';
import { FamiliesUserRemovedFromFamilyDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/families-user-removed-from-family.payload.dto';
import { SettingsUserGraduatedPayloadDTO } from '@superawesome/freekws-queue-messages/webhook/payloads/settings-user-graduated.payload.dto';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import { ESettingConsentType } from '@superawesome/freekws-settings-common';
import nock from 'nock';
import request from 'supertest';

import { App } from '../../src/app/app.entity';
import { AppLevelWebhookPayload, OrgLevelWebhookPayload } from '../../src/webhook/types';
import { Utils } from '../utils';
import { ORG_ENV_ID } from '../utils/constants';
import { KafkaTestHelper } from '../utils/kafka-helper';
import { makeRequest } from '../utils/request-helper';

const optOutDefinition = {
  ageBracket: {
    consentType: ESettingConsentType.OPT_OUT,
  },
};

const optInDefinition = {
  ageBracket: {
    consentType: ESettingConsentType.OPT_IN_VERIFIED,
  },
};

describe('WebhooksController (e2e)', () => {
  let app: INestApplication;
  const secret = 'superSecret';
  const userId = '1001';
  const serviceIdSettings = 'settings';
  const serviceIdFamilyGroup = 'family-group';
  let kafkaHelper: KafkaTestHelper;
  let testApp: App;

  beforeAll(async () => {
    app = await Utils.createTestServer();

    const config = Utils.configService;
    const kafkaConfig = config.getKafka();
    const topic = config.getAppWebhooksConfig().topicName;

    kafkaHelper = new KafkaTestHelper(kafkaConfig.kafkaHost, topic);
    await kafkaHelper.connect();
  });

  beforeEach(async () => {
    kafkaHelper.clearMessages();
    await Utils.cleanDb();
    const fixtures = await Utils.loadFixtures();
    testApp = fixtures.App.find((app) => app.name === 'test-app') as App;

    // Remove any existing nock interceptors
    nock.cleanAll();
  });

  afterAll(async () => {
    await kafkaHelper.disconnect();
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('POST /v1/webhooks/:orgEnvId/child-account-graduate', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.SETTINGS_USER_GRADUATED;
      const body: OrgLevelWebhookPayload<SettingsUserGraduatedPayloadDTO> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        payload: {
          userId: userId,
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdSettings, secret);

      await request(app.getHttpServer())
        .post(`/v1/webhooks/${ORG_ENV_ID}/child-account-graduated`)
        .set('x-kws-signature', `t=${timestamp},v1=${signature}`)
        .send(body)
        .expect(204);

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('child-account-graduated');
      expect(msg.payload.userId).toEqual(1001);
    });
  });

  describe('POST /v1/webhooks/:orgEnvId/user-removed-from-family', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.FAMILIES_USER_REMOVED_FROM_FAMILY;
      const body: OrgLevelWebhookPayload<FamiliesUserRemovedFromFamilyDTO> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        payload: {
          userId: userId,
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdFamilyGroup, secret);

      await request(app.getHttpServer())
        .post(`/v1/webhooks/${ORG_ENV_ID}/user-removed-from-family`)
        .set('x-kws-signature', `t=${timestamp},v1=${signature}`)
        .send(body)
        .expect(204);

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('parent-unlinked-from-child');
      expect(msg.payload.userId).toEqual(1001);
    });
  });

  describe('POST /v1/webhooks/:orgEnvId/settings-effective-values-changed', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.SETTINGS_EFFECTIVE_VALUES_CHANGED;
      const body: AppLevelWebhookPayload<SettingsEffectiveValuesChangedPayloadDTO<boolean>> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        productId: testApp.productId,
        payload: {
          userId: userId,
          settings: [
            {
              namespace: 'default',
              settingName: 'display-name',
              effectiveValue: true,
              effectiveSource: 'parentLimit',
              trigger: 'parentUpdate',
            },
            {
              namespace: 'Stranger',
              settingName: 'things',
              effectiveValue: true,
              effectiveSource: 'preference',
              trigger: 'user',
            },
            {
              namespace: 'default',
              settingName: 'complicated',
              effectiveValue: false,
              effectiveSource: 'enforcedLimit',
              trigger: 'system',
            },
          ],
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdSettings, secret);
      const settingsService = Utils.configService.getSettingsService();

      nock(settingsService.baseURL)
        .get(`/v1/settings/users/${userId}/products/${testApp.productId}/values`)
        .query((query) => query.format === 'full')
        .reply(200, {
          response: {
            settings: [
              {
                namespace: 'default',
                settingName: 'display-name',
                effectiveValue: true,
                definition: optOutDefinition,
              },
              {
                namespace: 'Stranger',
                settingName: 'things',
                effectiveValue: true,
                parentLimitedUpdatedAt: new Date().toISOString(),
                definition: optOutDefinition,
              },
              {
                namespace: 'default',
                settingName: 'complicated',
                effectiveValue: false,
                definition: optInDefinition,
              },
            ],
          },
          meta: { requestId: 'test-request-id', timestamp: new Date().toISOString() },
        });

      await request(app.getHttpServer())
        .post(`/v1/webhooks/${ORG_ENV_ID}/settings-effective-values-changed`)
        .set('x-kws-signature', `t=${timestamp},v1=${signature}`)
        .send(body)
        .expect(204);

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('user-permission-changed');
      expect(msg.payload.userId).toEqual(1001);
      expect(msg.payload.permissions).toEqual({
        'Stranger.things': true,
        'display-name': true,
      });
    });
  });

  describe('POST /v1/webhooks/:orgEnvId/user-added-to-family', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.FAMILIES_USER_ADDED_TO_FAMILY;
      const body: OrgLevelWebhookPayload<FamiliesUserAddedToFamilyDTO> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        payload: {
          userId: userId,
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdFamilyGroup, secret);

      await request(app.getHttpServer())
        .post(`/v1/webhooks/${ORG_ENV_ID}/user-added-to-family`)
        .set('x-kws-signature', `t=${timestamp},v1=${signature}`)
        .send(body)
        .expect(204);

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('child-linked-to-parent');
      expect(msg.payload.userId).toEqual(1001);
      expect(msg.payload.parentId).toEqual(1001);
    });
  });

  describe('POST /v1/webhooks/:orgEnvId/guardian-request-expired', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.FAMILIES_GUARDIAN_REQUEST_EXPIRED;
      const body: OrgLevelWebhookPayload<FamiliesGuardianRequestExpiredPayloadDto> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        payload: {
          userId: userId,
          guardianRequestId: '5004f8df-f87b-4512-b9f2-18ea8a5860ba',
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdFamilyGroup, secret);

      await request(app.getHttpServer())
        .post(`/v1/webhooks/${ORG_ENV_ID}/guardian-request-expired`)
        .set('x-kws-signature', `t=${timestamp},v1=${signature}`)
        .send(body)
        .expect(204);

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('unresponsive-parent-account-deleted');
      expect(msg.payload.userIds).toEqual([1001]);
    });
  });

  describe('POST /v1/webhooks/:orgEnvId/families-group-deleted', () => {
    it('should return the expected response', async () => {
      const timestamp = Date.now();
      const webhookName = EWebhookName.FAMILIES_FAMILY_GROUP_DELETED;
      const body: OrgLevelWebhookPayload<FamiliesGroupDeletedPayloadDto> = {
        name: webhookName,
        time: timestamp,
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        payload: {
          familyGroupId: '11',
          orgEnvId: ORG_ENV_ID,
          orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
          members: [
            {
              id: '4234',
              userId: '********',
              role: EMemberRole.Manager,
              email: '<EMAIL>',
            },
            {
              id: '4234',
              userId: '********',
              role: EMemberRole.Supervised,
              email: '<EMAIL>',
            },
          ],
        },
      };

      const signature = Utils.generateKwsSignature(timestamp, body, secret);
      Utils.mockCallbackServiceWebhook(webhookName, ORG_ENV_ID, serviceIdFamilyGroup, secret);

      await makeRequest(app, 'post', `/v1/webhooks/${ORG_ENV_ID}/families-group-deleted`, {
        headers: {
          'x-kws-signature': `t=${timestamp},v1=${signature}`,
        },
        body,
        expectedStatus: 204,
      });

      const msg = await kafkaHelper.getNextMessage();
      expect(msg.name).toEqual('parent-account-deleted');
      expect(msg.payload.parentId).toEqual(********);
    });
  });
});

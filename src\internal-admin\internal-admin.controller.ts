import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  UpdateUserDateOfBirthDTO,
  UserSearchQuery,
  UserSearchListResponseDTO,
} from '@superawesome/freekws-classic-wrapper-common';
import { CheckPolicies, KeycloakAuthGuard, PoliciesGuard } from '@superawesome/freekws-nestjs-guards';
import { And, HasAzp } from '@superawesome/freekws-nestjs-guards/policies';
import { TransformResponseInterceptor } from '@superawesome/freekws-nestjs-http-interceptors/interceptors/transform-response.interceptor';
import { UserSettingValueDTO } from '@superawesome/freekws-settings-common';
import { plainToInstance } from 'class-transformer';

import { InternalAdminService } from './internal-admin.service';
import { NIANTIC_ORG_ENV_ID } from '../app/app.service';
import { EAPITags } from '../common/types';
import { UpdateUserDOBSchema } from '../org-env/types';
import { UserDeletionDTO } from '../user/user.dto';
import { UserService } from '../user/user.service';

@Controller('/internal-admin')
@UseGuards(KeycloakAuthGuard)
@UseInterceptors(TransformResponseInterceptor)
export class InternalAdminController {
  constructor(private readonly internalAdminService: InternalAdminService, private readonly userService: UserService) {}

  @ApiOperation({
    summary: "Internal Admin Endpoint: Update the user's dob",
    description: `Intended only for classic customers. Update the user's DOB by requesting settings with a provided dob and location.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse({ schema: UpdateUserDOBSchema })
  @Put('/org-envs/:orgEnvId/users/:userId/dob')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async updateUserDOB(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() { dateOfBirth, location }: UpdateUserDateOfBirthDTO,
  ): Promise<UserSettingValueDTO[]> {
    return await this.internalAdminService.getUserSettingsToUpdateUserDOB(orgEnvId, {
      userId,
      dateOfBirth,
      location,
    });
  }

  @ApiOperation({
    summary: "Internal Admin Endpoint: Delete a user's account",
    description: `Intended only for classic customers. Delete a user's account.`,
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse()
  @Post('/org-envs/:orgEnvId/users/:userId/delete-account')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async deleteUserAccount(
    @Param('orgEnvId') orgEnvId: string,
    @Param('userId', new ParseIntPipe()) userId: number,
    @Body() body: UserDeletionDTO,
  ) {
    if (body.password) {
      throw new BadRequestException('Password is deprecated and should not be used');
    }

    await this.internalAdminService.deleteUserAccount(orgEnvId, userId);
  }

  @ApiOperation({
    summary: 'Internal Admin Endpoint: Search users by username. Only works for Niantic.',
    description: 'Search for users by username prefix. Returns up to 10 matching users with their parent emails.',
  })
  @ApiTags(EAPITags.InternalAdmin)
  @ApiOkResponse({ type: UserSearchListResponseDTO })
  @Get('/users')
  @HttpCode(HttpStatus.OK)
  @UseGuards(PoliciesGuard)
  @CheckPolicies({
    useClass: And,
    inject: [new HasAzp('devportal-api')],
  })
  async searchUsersByUsername(@Query() queryParams: UserSearchQuery): Promise<UserSearchListResponseDTO> {
    // Endpoint will only be used by Niantic, so we can hardcode the orgEnvId
    const users = await this.userService.searchUsersByUsername(queryParams.username, NIANTIC_ORG_ENV_ID);
    return plainToInstance(UserSearchListResponseDTO, { users: users });
  }
}

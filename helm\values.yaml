freekws-classic-wrapper-backend-sa-deployment:
  name: freekws-classic-wrapper-backend
  restrictedSecurityContext: true
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 10%
      maxSurge: 10%
  containers:
    freekws-classic-wrapper-backend-api:
      jobs:
        - name: freekws-classic-wrapper-backend-migration
          kind: Job
          backoffLimit: 1
          maxTime: 1800 # in seconds this is 30 minutes
          preUpgradeHook: true
          args:
            - npm
            - run
            - migration:run:prod
        - name: refresh-token-cleanup
          kind: CronJob
          maxTime: 7200 # 2 hours
          schedule: '0 12 * * *'
          args:
            - /bin/sh
            - -c
            - npm run job DeleteOldRefreshTokens
      image:
        name: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend
        tag: main
      env:
        NODE_ENV: "production"
        KEYCLOAK_BASE_URL: ""
        KEYCLOAK_CLIENT_SECRET: ""
        KEYCLOAK_TIMEOUT_MS: "30000"
        EMAIL_KAFKA_TOPIC: ""
        EMAIL_SA_LOGO_SRC: ""
        EMAIL_SA_DEFAULT_HEADER_SRC: ""
      readinessProbe:
        httpGet:
          path: /healthcheck
          port: 80
        periodSeconds: 5
      livenessProbe:
        httpGet:
          path: /healthcheck
          port: 80
        periodSeconds: 5
      ports:
        - name: http
          containerPort: 80
          servicePort: 80
          type: Ingress # Ingress, LoadBalancer, ClusterIP
          class: alb # alb, traefik
          customWebACL: 1
          path: /*
          healthcheck: /healthcheck
          scheme: internet-facing
          groupName: freekws-classic-wrapper-backend
          hosts:
      resources:
        limits:
          cpu: 1000m
          memory: 512Mi
        requests:
          cpu: 300m
          memory: 300Mi
  customDNS:
    enabled: false
